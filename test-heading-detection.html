<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heading Detection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .debug-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .test-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        code {
            background: #f1f3f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>Heading Detection Debug Guide</h1>
        
        <div class="debug-box">
            <h4>🔍 Debug Steps for "No Headings Found" Issue</h4>
            <p>Follow these steps to diagnose why the TOC isn't finding headings:</p>
        </div>

        <h2>Step 1: Check Plugin Settings</h2>
        <div class="test-box">
            <p><strong>WordPress Admin → Safeoid TOC Settings</strong></p>
            <ul>
                <li>Verify which heading levels are enabled (H2, H3, H4, etc.)</li>
                <li>Check if auto-insert is enabled for your post type</li>
                <li>Ensure the post type is selected (Posts, Pages, etc.)</li>
            </ul>
        </div>

        <h2>Step 2: Use Debug Shortcodes</h2>
        <div class="test-box">
            <p>Add these shortcodes to your post/page to get detailed debug information:</p>
            
            <h4>Basic Debug Info:</h4>
            <pre><code>[safeoid_toc_debug]</code></pre>
            
            <h4>Content Processing Test:</h4>
            <pre><code>[safeoid_toc_test]</code></pre>
            
            <p>These will show you:</p>
            <ul>
                <li>What headings the plugin is looking for</li>
                <li>Raw vs filtered content</li>
                <li>Regex patterns being used</li>
                <li>Actual matches found</li>
            </ul>
        </div>

        <h2>Step 3: Check Your Content</h2>
        <div class="test-box">
            <p>Common issues with heading detection:</p>
            
            <h4>✅ Valid Heading Examples:</h4>
            <pre><code>&lt;h2&gt;This is a valid heading&lt;/h2&gt;
&lt;h3 class="my-class"&gt;Heading with class&lt;/h3&gt;
&lt;h4 id="my-id"&gt;Heading with ID&lt;/h4&gt;</code></pre>
            
            <h4>❌ Invalid/Problematic Examples:</h4>
            <pre><code>&lt;h2&gt;&lt;/h2&gt;  <!-- Empty heading -->
&lt;h2&gt;   &lt;/h2&gt;  <!-- Only whitespace -->
&lt;div class="h2"&gt;Not a real heading&lt;/div&gt;  <!-- CSS styled div -->
&lt;h2&gt;&lt;img src="image.jpg"&gt;&lt;/h2&gt;  <!-- Only image, no text --></code></pre>
        </div>

        <h2>Step 4: Test Different Scenarios</h2>
        <div class="test-box">
            <h4>Test with Simple Content:</h4>
            <p>Create a test post with just:</p>
            <pre><code>&lt;h2&gt;First Heading&lt;/h2&gt;
&lt;p&gt;Some content here.&lt;/p&gt;

&lt;h3&gt;Second Heading&lt;/h3&gt;
&lt;p&gt;More content here.&lt;/p&gt;</code></pre>
            
            <p>Then add the shortcode: <code>[safeoid_toc]</code></p>
        </div>

        <h2>Step 5: Check for Plugin Conflicts</h2>
        <div class="test-box">
            <p>Some plugins can interfere with content processing:</p>
            <ul>
                <li><strong>Page Builders:</strong> Elementor, Gutenberg blocks, etc.</li>
                <li><strong>Caching Plugins:</strong> May cache old content</li>
                <li><strong>Content Filters:</strong> Other plugins modifying content</li>
                <li><strong>Minification:</strong> HTML minification removing spaces</li>
            </ul>
            
            <p><strong>Test:</strong> Temporarily deactivate other plugins and test again.</p>
        </div>

        <h2>Step 6: Manual Testing</h2>
        <div class="test-box">
            <p>Test the regex pattern manually:</p>
            
            <h4>Default Pattern:</h4>
            <pre><code>/<(h2|h3|h4)([^>]*)>(.*?)<\/\1>/is</code></pre>
            
            <p>This pattern looks for:</p>
            <ul>
                <li><code>(h2|h3|h4)</code> - Heading tags H2, H3, or H4</li>
                <li><code>([^>]*)</code> - Any attributes</li>
                <li><code>(.*?)</code> - The heading text content</li>
                <li><code><\/\1></code> - The closing tag</li>
            </ul>
        </div>

        <h2>Step 7: Common Solutions</h2>
        <div class="test-box">
            <h4>If headings still not found:</h4>
            
            <ol>
                <li><strong>Check heading levels:</strong> Make sure you're using H2, H3, H4 (default settings)</li>
                <li><strong>Clear cache:</strong> Clear any caching plugins</li>
                <li><strong>Check post type:</strong> Ensure TOC is enabled for your post type</li>
                <li><strong>Test with shortcode:</strong> Use <code>[safeoid_toc]</code> instead of auto-insert</li>
                <li><strong>Check content source:</strong> Some page builders store content differently</li>
            </ol>
        </div>

        <h2>Step 8: WordPress Debug Log</h2>
        <div class="test-box">
            <p>Enable WordPress debug logging to see detailed information:</p>
            
            <h4>Add to wp-config.php:</h4>
            <pre><code>define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);</code></pre>
            
            <p>Then check <code>/wp-content/debug.log</code> for entries like:</p>
            <pre><code>Safeoid TOC: Found X headings in content
Safeoid TOC: Looking for headings: h2, h3, h4
Safeoid TOC: Content length: XXXX</code></pre>
        </div>

        <h2>Expected Results</h2>
        <div class="debug-box">
            <h4>✅ When Working Correctly:</h4>
            <ul>
                <li>Debug shortcode shows headings found</li>
                <li>TOC appears with clickable links</li>
                <li>Rank Math shows green checkmark</li>
                <li>No "No headings found" message</li>
            </ul>
            
            <h4>🔧 If Still Not Working:</h4>
            <p>Share the output of <code>[safeoid_toc_debug]</code> and <code>[safeoid_toc_test]</code> for further diagnosis.</p>
        </div>
    </div>
</body>
</html>
