<?php
/**
 * Plugin Name: Side Responsive TOC by Safeoid
 * Plugin URI: https://safeoid.com/floating-toc
 * Description: A lightweight, responsive Table of Contents plugin with floating sidebar on desktop and sticky header on mobile. Includes Elementor integration and SEO schema markup.
 * Version: 1.0.0
 * Author: Safeoid
 * License: GPLv2 or later
 * Text Domain: safeoid-toc
 * Requires at least: 6.5
 * Tested up to: 6.5
 * Requires PHP: 7.4
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Check if plugin is already loaded
if (!class_exists('SafeoidTOC')) {

// Define plugin constants
define('SAFEOID_TOC_VERSION', '1.0.0');
define('SAFEOID_TOC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SAFEOID_TOC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SAFEOID_TOC_TEXT_DOMAIN', 'safeoid-toc');

/**
 * Main plugin class
 */
class SafeoidTOC {

    private static $instance;
    private $option_prefix = 'safeoid_toc_';

    public static function get_instance() {
        if (!isset(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        // Initialize plugin
        add_action('plugins_loaded', array($this, 'init'));
    }
    
    public function init() {
        // Load text domain
        load_plugin_textdomain(SAFEOID_TOC_TEXT_DOMAIN, false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Enqueue assets
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Admin settings
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));

        // Content filter
        add_filter('the_content', array($this, 'generate_toc'), 10);

        // Shortcode
        add_shortcode('safeoid_toc', array($this, 'toc_shortcode'));

        // Elementor widget
        add_action('elementor/widgets/register', array($this, 'register_elementor_widget'));
    }

    public function activate() {
        // Set default options
        $defaults = array(
            'headings' => array('h2', 'h3', 'h4'),
            'post_types' => array('post', 'page'),
            'auto_insert' => true,
            'toc_label' => __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN),
            'enable_desktop' => true,
            'enable_mobile' => true,
            'text_color' => '#111',
            'link_color' => '#0073e6',
            'background_color' => '#ffffff',
            'disable_schema' => false
        );

        foreach ($defaults as $key => $value) {
            $option_name = $this->option_prefix . $key;
            if (false === get_option($option_name)) {
                add_option($option_name, $value);
            }
        }
    }

    public function deactivate() {
        // Clear transients on deactivation
        $this->clear_transients();
    }

    private function clear_transients() {
        global $wpdb;
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM $wpdb->options WHERE option_name LIKE %s OR option_name LIKE %s",
                '_transient_safeoid_toc_%',
                '_transient_timeout_safeoid_toc_%'
            )
        );
    }

    public function enqueue_scripts() {
        if (function_exists('is_amp_endpoint') && is_amp_endpoint()) {
            return;
        }

        wp_enqueue_style(
            'safeoid-toc-style',
            SAFEOID_TOC_PLUGIN_URL . 'css/style.css',
            array(),
            SAFEOID_TOC_VERSION
        );

        wp_enqueue_script(
            'safeoid-toc-js',
            SAFEOID_TOC_PLUGIN_URL . 'js/toc.js',
            array(),
            SAFEOID_TOC_VERSION,
            true
        );
    }
    
    public function add_admin_menu() {
        add_menu_page(
            __('Safeoid TOC Settings', SAFEOID_TOC_TEXT_DOMAIN),
            __('Safeoid TOC', SAFEOID_TOC_TEXT_DOMAIN),
            'manage_options',
            'safeoid-toc-settings',
            array($this, 'settings_page'),
            'dashicons-list-view',
            82
        );
    }

    public function settings_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['safeoid_toc_nonce'], 'safeoid_toc_settings')) {
            $settings = array(
                'headings' => isset($_POST['safeoid_toc_headings']) ? array_map('sanitize_text_field', $_POST['safeoid_toc_headings']) : array(),
                'post_types' => isset($_POST['safeoid_toc_post_types']) ? array_map('sanitize_text_field', $_POST['safeoid_toc_post_types']) : array(),
                'auto_insert' => isset($_POST['safeoid_toc_auto_insert']),
                'toc_label' => sanitize_text_field($_POST['toc_label']),
                'enable_desktop' => isset($_POST['enable_desktop']),
                'enable_mobile' => isset($_POST['enable_mobile']),
                'text_color' => sanitize_hex_color($_POST['text_color']),
                'link_color' => sanitize_hex_color($_POST['link_color']),
                'background_color' => sanitize_hex_color($_POST['background_color']),
                'disable_schema' => isset($_POST['disable_schema'])
            );

            foreach ($settings as $key => $value) {
                update_option($this->option_prefix . $key, $value);
            }

            echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', SAFEOID_TOC_TEXT_DOMAIN) . '</p></div>';
        }

        include_once SAFEOID_TOC_PLUGIN_DIR . 'inc/settings-page.php';
    }

    public function register_settings() {
        $settings = array('headings', 'post_types', 'auto_insert', 'toc_label', 'enable_desktop', 'enable_mobile', 'text_color', 'link_color', 'background_color', 'disable_schema');

        foreach ($settings as $setting) {
            register_setting(
                'safeoid_toc_settings',
                $this->option_prefix . $setting
            );
        }
    }
    
    public function generate_toc($content) {
        if (!in_the_loop() || !is_main_query()) {
            return $content;
        }

        if (has_shortcode($content, 'safeoid_toc')) {
            return $content;
        }

        $post_types = (array) get_option($this->option_prefix . 'post_types', array('post', 'page'));
        $auto_insert = get_option($this->option_prefix . 'auto_insert', true);

        if (!is_singular($post_types) || !$auto_insert) {
            return $content;
        }

        return $this->build_toc($content);
    }

    public function build_toc($content) {
        $headings = (array) get_option($this->option_prefix . 'headings', array('h2', 'h3', 'h4'));
        if (empty($headings)) {
            return $content;
        }

        $pattern = '/<(' . implode('|', array_map('preg_quote', $headings)) . ')([^>]*)>(.*?)<\/\1>/is';
        preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);

        if (empty($matches)) {
            return $content;
        }

        $toc_label = get_option($this->option_prefix . 'toc_label', __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN));

        // Add CSS variables for colors
        $text_color = get_option($this->option_prefix . 'text_color', '#111');
        $link_color = get_option($this->option_prefix . 'link_color', '#0073e6');
        $background_color = get_option($this->option_prefix . 'background_color', '#ffffff');

        $toc = '<style>';
        $toc .= ':root {';
        $toc .= '--safeoid-toc-text-color: ' . esc_attr($text_color) . ';';
        $toc .= '--safeoid-toc-link-color: ' . esc_attr($link_color) . ';';
        $toc .= '--safeoid-toc-bg-color: ' . esc_attr($background_color) . ';';
        $toc .= '}';
        $toc .= '</style>';

        $toc .= '<div class="floating-toc" aria-label="' . esc_attr($toc_label) . '">';
        $toc .= '<h3 class="toc-heading">' . esc_html($toc_label) . '</h3>';
        $toc .= '<div class="toc-drag-handle" title="Click and drag to move" aria-label="Drag handle to move table of contents"></div>';
        $toc .= '<div class="toc-toggle">☰ ' . esc_html($toc_label) . '</div>';
        $toc .= '<div class="toc-content"><ul>';

        $jsonLD = array(
            "@context" => "https://schema.org",
            "@type" => "ItemList",
            "itemListElement" => array()
        );

        $count = 0;
        $used_anchors = array();

        foreach ($matches as $match) {
            $tag = strtolower($match[1]);
            $attrs = $match[2];
            $title = wp_strip_all_tags($match[3]);

            if (empty($title)) {
                continue;
            }

            preg_match('/id=["\']([^"\']+)["\']/i', $attrs, $id_matches);
            $anchor = !empty($id_matches[1]) ? $id_matches[1] : '';

            if (empty($anchor)) {
                $anchor = $this->generate_heading_id($title) ?: 'toc-section-' . $count;

                $base_anchor = $anchor;
                $i = 1;
                while (in_array($anchor, $used_anchors)) {
                    $anchor = $base_anchor . '-' . $i++;
                }

                $used_anchors[] = $anchor;

                // Add ID to the heading in content
                $new_heading = '<' . $tag . ' id="' . esc_attr($anchor) . '"' . $attrs . '>' . $title . '</' . $tag . '>';
                $content = str_replace($match[0], $new_heading, $content);

                // Also add JavaScript fallback to ensure ID is added
                add_action('wp_footer', function() use ($anchor, $title, $tag) {
                    echo '<script>
                    document.addEventListener("DOMContentLoaded", function() {
                        // Find heading by text and add ID if missing
                        var headings = document.querySelectorAll("' . $tag . '");
                        headings.forEach(function(heading) {
                            if (heading.textContent.trim() === "' . esc_js($title) . '" && !heading.id) {
                                heading.id = "' . esc_js($anchor) . '";
                                console.log("Added ID to heading:", "' . esc_js($anchor) . '");
                            }
                        });
                    });
                    </script>';
                }, 999);
            } else {
                $used_anchors[] = $anchor;
            }

            $toc .= '<li><a href="#' . esc_attr($anchor) . '">' . esc_html($title) . '</a></li>';

            $jsonLD['itemListElement'][] = array(
                "@type" => "ListItem",
                "position" => $count + 1,
                "name" => $title,
                "url" => get_permalink() . '#' . $anchor
            );

            $count++;
        }

        $toc .= '</ul></div></div>';

        // Add schema markup if not disabled
        $disable_schema = get_option($this->option_prefix . 'disable_schema', false);
        if (!$disable_schema) {
            $jsonLDScript = '<script type="application/ld+json">' . wp_json_encode($jsonLD) . '</script>';
            $toc = $jsonLDScript . $toc;
        }

        // Debug output for administrators
        if (current_user_can('manage_options') && isset($_GET['toc_debug'])) {
            $toc .= '<div style="background: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px;">';
            $toc .= '<strong>TOC Debug Info:</strong><br>';
            $toc .= 'Found ' . count($matches) . ' headings<br>';
            $toc .= 'Headings setting: ' . implode(', ', $headings) . '<br>';
            $toc .= 'Used anchors: ' . implode(', ', $used_anchors) . '<br>';
            $toc .= '</div>';
        }

        return $toc . $content;
    }
    
    public function toc_shortcode($atts = array(), $content = null) {
        $atts = shortcode_atts(array(
            'headings' => implode(',', (array) get_option($this->option_prefix . 'headings', array('h2', 'h3', 'h4')))
        ), $atts);

        $headings = array_filter(
            array_map('trim', explode(',', $atts['headings'])),
            function($h) {
                return in_array(strtolower($h), array('h1', 'h2', 'h3', 'h4', 'h5', 'h6'));
            }
        );

        if (empty($headings)) {
            $headings = array('h2', 'h3', 'h4');
        }

        // Temporarily set headings for this shortcode
        $original_headings = get_option($this->option_prefix . 'headings');
        update_option($this->option_prefix . 'headings', $headings);

        global $post;
        $content = $content ?: (is_a($post, 'WP_Post') ? $post->post_content : '');
        $result = $this->build_toc(apply_filters('the_content', $content));

        // Restore original headings
        update_option($this->option_prefix . 'headings', $original_headings);

        return $result;
    }

    public function register_elementor_widget($widgets_manager) {
        if (file_exists(SAFEOID_TOC_PLUGIN_DIR . 'widgets/elementor-toc-widget.php')) {
            require_once SAFEOID_TOC_PLUGIN_DIR . 'widgets/elementor-toc-widget.php';
            $widgets_manager->register(new SafeoidTOCElementorWidget());
        }
    }

    /**
     * Generate a clean ID from heading text
     */
    private function generate_heading_id($text) {
        // Remove HTML tags
        $text = wp_strip_all_tags($text);

        // Convert to lowercase and replace spaces with hyphens
        $id = strtolower(trim($text));
        $id = preg_replace('/[^a-z0-9\-_]/', '', str_replace(' ', '-', $id));

        // Remove multiple consecutive hyphens
        $id = preg_replace('/-+/', '-', $id);

        // Remove leading/trailing hyphens
        $id = trim($id, '-');

        // Ensure ID is not empty
        if (empty($id)) {
            $id = 'heading-' . uniqid();
        }

        return $id;
    }

}

// Initialize the plugin
SafeoidTOC::get_instance();

} // End class_exists check
