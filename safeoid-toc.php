<?php
/**
 * Plugin Name: Side Responsive TOC by Safeoid
 * Plugin URI: https://safeoid.com/floating-toc
 * Description: A lightweight, responsive Table of Contents plugin with floating sidebar on desktop and sticky header on mobile. Includes Elementor integration and SEO schema markup.
 * Version: 1.0.0
 * Author: Safeoid
 * License: GPLv2 or later
 * Text Domain: safeoid-toc
 * Requires at least: 6.5
 * Tested up to: 6.5
 * Requires PHP: 7.4
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Check if plugin is already loaded
if (!class_exists('SafeoidTOC')) {

// Define plugin constants
define('SAFEOID_TOC_VERSION', '1.0.0');
define('SAFEOID_TOC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SAFEOID_TOC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SAFEOID_TOC_TEXT_DOMAIN', 'safeoid-toc');

/**
 * Main plugin class
 */
class SafeoidTOC {

    private static $instance;
    private $option_prefix = 'safeoid_toc_';

    public static function get_instance() {
        if (!isset(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        // Initialize plugin
        add_action('plugins_loaded', array($this, 'init'));
    }
    
    public function init() {
        // Load text domain
        load_plugin_textdomain(SAFEOID_TOC_TEXT_DOMAIN, false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Enqueue assets
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Admin settings
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));

        // Content filter
        add_filter('the_content', array($this, 'generate_toc'), 10);

        // Shortcode
        add_shortcode('safeoid_toc', array($this, 'toc_shortcode'));

        // Elementor widget - use multiple hooks for compatibility
        add_action('elementor/widgets/register', array($this, 'register_elementor_widget'));
        add_action('elementor/widgets/widgets_registered', array($this, 'register_elementor_widget_legacy'));

        // Fallback for very old Elementor versions
        add_action('elementor/widgets/widgets_registered', array($this, 'register_elementor_widget_old'), 10, 1);

        // Check if Elementor is active
        add_action('plugins_loaded', array($this, 'check_elementor_compatibility'));

        // Add admin notices for debugging
        add_action('admin_notices', array($this, 'elementor_admin_notices'));

        // Add debug shortcode for testing
        add_shortcode('safeoid_toc_debug', array($this, 'debug_shortcode'));

        // Add Rank Math compatibility hooks
        add_action('wp_head', array($this, 'add_rank_math_compatibility'));
        add_filter('rank_math/json_ld', array($this, 'add_toc_schema'), 10, 2);

        // CRITICAL: Add our plugin to Rank Math's TOC detection list
        add_filter('rank_math/researches/toc_plugins', array($this, 'register_with_rank_math'));
    }

    public function activate() {
        // Set default options
        $defaults = array(
            'headings' => array('h2', 'h3', 'h4'),
            'post_types' => array('post', 'page'),
            'auto_insert' => true,
            'toc_label' => __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN),
            'enable_desktop' => true,
            'enable_mobile' => true,
            'text_color' => '#111',
            'link_color' => '#0073e6',
            'background_color' => '#ffffff',
            'disable_schema' => false
        );

        foreach ($defaults as $key => $value) {
            $option_name = $this->option_prefix . $key;
            if (false === get_option($option_name)) {
                add_option($option_name, $value);
            }
        }
    }

    public function deactivate() {
        // Clear transients on deactivation
        $this->clear_transients();
    }

    private function clear_transients() {
        global $wpdb;
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM $wpdb->options WHERE option_name LIKE %s OR option_name LIKE %s",
                '_transient_safeoid_toc_%',
                '_transient_timeout_safeoid_toc_%'
            )
        );
    }

    public function enqueue_scripts() {
        if (function_exists('is_amp_endpoint') && is_amp_endpoint()) {
            return;
        }

        wp_enqueue_style(
            'safeoid-toc-style',
            SAFEOID_TOC_PLUGIN_URL . 'css/style.css',
            array(),
            SAFEOID_TOC_VERSION
        );

        wp_enqueue_script(
            'safeoid-toc-js',
            SAFEOID_TOC_PLUGIN_URL . 'js/toc.js',
            array(),
            SAFEOID_TOC_VERSION,
            true
        );
    }
    
    public function add_admin_menu() {
        add_menu_page(
            __('Safeoid TOC Settings', SAFEOID_TOC_TEXT_DOMAIN),
            __('Safeoid TOC', SAFEOID_TOC_TEXT_DOMAIN),
            'manage_options',
            'safeoid-toc-settings',
            array($this, 'settings_page'),
            'dashicons-list-view',
            82
        );
    }

    public function settings_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['safeoid_toc_nonce'], 'safeoid_toc_settings')) {
            $settings = array(
                'headings' => isset($_POST['safeoid_toc_headings']) ? array_map('sanitize_text_field', $_POST['safeoid_toc_headings']) : array(),
                'post_types' => isset($_POST['safeoid_toc_post_types']) ? array_map('sanitize_text_field', $_POST['safeoid_toc_post_types']) : array(),
                'auto_insert' => isset($_POST['safeoid_toc_auto_insert']),
                'toc_label' => sanitize_text_field($_POST['toc_label']),
                'enable_desktop' => isset($_POST['enable_desktop']),
                'enable_mobile' => isset($_POST['enable_mobile']),
                'text_color' => sanitize_hex_color($_POST['text_color']),
                'link_color' => sanitize_hex_color($_POST['link_color']),
                'background_color' => sanitize_hex_color($_POST['background_color']),
                'disable_schema' => isset($_POST['disable_schema'])
            );

            foreach ($settings as $key => $value) {
                update_option($this->option_prefix . $key, $value);
            }

            echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', SAFEOID_TOC_TEXT_DOMAIN) . '</p></div>';
        }

        include_once SAFEOID_TOC_PLUGIN_DIR . 'inc/settings-page.php';
    }

    public function register_settings() {
        $settings = array('headings', 'post_types', 'auto_insert', 'toc_label', 'enable_desktop', 'enable_mobile', 'text_color', 'link_color', 'background_color', 'disable_schema');

        foreach ($settings as $setting) {
            register_setting(
                'safeoid_toc_settings',
                $this->option_prefix . $setting
            );
        }
    }
    
    public function generate_toc($content) {
        if (!in_the_loop() || !is_main_query()) {
            return $content;
        }

        if (has_shortcode($content, 'safeoid_toc')) {
            return $content;
        }

        $post_types = (array) get_option($this->option_prefix . 'post_types', array('post', 'page'));
        $auto_insert = get_option($this->option_prefix . 'auto_insert', false); // Changed default from true to false

        if (!is_singular($post_types) || !$auto_insert) {
            return $content;
        }

        return $this->build_toc($content);
    }

    public function build_toc($content) {
        $headings = (array) get_option($this->option_prefix . 'headings', array('h2', 'h3', 'h4'));
        if (empty($headings)) {
            return $content;
        }

        // Improved regex pattern for better heading detection
        $pattern = '/<(' . implode('|', $headings) . ')([^>]*)>(.*?)<\/\1>/is';
        preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);

        // Debug: Log heading detection for troubleshooting
        if (defined('WP_DEBUG') && WP_DEBUG && current_user_can('manage_options')) {
            error_log('Safeoid TOC: Found ' . count($matches) . ' headings in content');
            error_log('Safeoid TOC: Looking for headings: ' . implode(', ', $headings));
            error_log('Safeoid TOC: Content length: ' . strlen($content));
        }

        if (empty($matches)) {
            // Try alternative method if no headings found
            $matches = $this->extract_headings_alternative($content, $headings);

            if (empty($matches)) {
                // Add a notice for debugging
                if (defined('WP_DEBUG') && WP_DEBUG && current_user_can('manage_options')) {
                    error_log('Safeoid TOC: No headings found in content. Content preview: ' . substr(strip_tags($content), 0, 200));
                }
                return $content . '<!-- Safeoid TOC: No headings found -->';
            }
        }

        $toc_label = get_option($this->option_prefix . 'toc_label', __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN));

        // Add CSS variables for colors
        $text_color = get_option($this->option_prefix . 'text_color', '#111');
        $link_color = get_option($this->option_prefix . 'link_color', '#0073e6');
        $background_color = get_option($this->option_prefix . 'background_color', '#ffffff');

        $toc = '<style>';
        $toc .= ':root {';
        $toc .= '--safeoid-toc-text-color: ' . esc_attr($text_color) . ';';
        $toc .= '--safeoid-toc-link-color: ' . esc_attr($link_color) . ';';
        $toc .= '--safeoid-toc-bg-color: ' . esc_attr($background_color) . ';';
        $toc .= '}';
        $toc .= '</style>';

        // Add Rank Math compatible classes and schema for TOC detection
        $toc .= '<div class="floating-toc ez-toc-container toc-container" aria-label="' . esc_attr($toc_label) . '" itemscope itemtype="https://schema.org/ItemList">';
        $toc .= '<h3 class="toc-heading ez-toc-title" itemprop="name">' . esc_html($toc_label) . '</h3>';
        $toc .= '<div class="toc-drag-handle" title="Click and drag to move" aria-label="Drag handle to move table of contents"></div>';
        $toc .= '<div class="toc-toggle">☰ ' . esc_html($toc_label) . '</div>';
        $toc .= '<div class="toc-content"><ul class="ez-toc-list toc-list">';

        $jsonLD = array(
            "@context" => "https://schema.org",
            "@type" => "ItemList",
            "itemListElement" => array()
        );

        $count = 0;
        $used_anchors = array();

        foreach ($matches as $match) {
            $tag = strtolower($match[1]);
            $attrs = $match[2];
            $title = wp_strip_all_tags($match[3]);

            if (empty($title)) {
                continue;
            }

            preg_match('/id=["\']([^"\']+)["\']/i', $attrs, $id_matches);
            $anchor = !empty($id_matches[1]) ? $id_matches[1] : '';

            if (empty($anchor)) {
                $anchor = $this->generate_heading_id($title) ?: 'toc-section-' . $count;

                $base_anchor = $anchor;
                $i = 1;
                while (in_array($anchor, $used_anchors)) {
                    $anchor = $base_anchor . '-' . $i++;
                }

                $used_anchors[] = $anchor;

                // Add ID to the heading in content
                $new_heading = '<' . $tag . ' id="' . esc_attr($anchor) . '"' . $attrs . '>' . $title . '</' . $tag . '>';
                $content = str_replace($match[0], $new_heading, $content);

                // Also add JavaScript fallback to ensure ID is added
                add_action('wp_footer', function() use ($anchor, $title, $tag) {
                    echo '<script>
                    document.addEventListener("DOMContentLoaded", function() {
                        // Find heading by text and add ID if missing
                        var headings = document.querySelectorAll("' . $tag . '");
                        headings.forEach(function(heading) {
                            if (heading.textContent.trim() === "' . esc_js($title) . '" && !heading.id) {
                                heading.id = "' . esc_js($anchor) . '";
                                console.log("Added ID to heading:", "' . esc_js($anchor) . '");
                            }
                        });
                    });
                    </script>';
                }, 999);
            } else {
                $used_anchors[] = $anchor;
            }

            // Add Rank Math compatible classes and schema for TOC links
            $heading_level = (int) substr($tag, 1); // Extract number from h1, h2, etc.
            $toc .= '<li class="ez-toc-page-1 ez-toc-heading-level-' . $heading_level . '" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
            $toc .= '<a class="ez-toc-link" href="#' . esc_attr($anchor) . '" itemprop="url"><span itemprop="name">' . esc_html($title) . '</span></a>';
            $toc .= '<meta itemprop="position" content="' . ($count + 1) . '">';
            $toc .= '</li>';

            $jsonLD['itemListElement'][] = array(
                "@type" => "ListItem",
                "position" => $count + 1,
                "name" => $title,
                "url" => get_permalink() . '#' . $anchor
            );

            $count++;
        }

        $toc .= '</ul></div></div>';

        // Add schema markup if not disabled
        $disable_schema = get_option($this->option_prefix . 'disable_schema', false);
        if (!$disable_schema) {
            $jsonLDScript = '<script type="application/ld+json">' . wp_json_encode($jsonLD) . '</script>';
            $toc = $jsonLDScript . $toc;
        }

        // Debug output for administrators
        if (current_user_can('manage_options') && isset($_GET['toc_debug'])) {
            $toc .= '<div style="background: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px;">';
            $toc .= '<strong>TOC Debug Info:</strong><br>';
            $toc .= 'Found ' . count($matches) . ' headings<br>';
            $toc .= 'Headings setting: ' . implode(', ', $headings) . '<br>';
            $toc .= 'Used anchors: ' . implode(', ', $used_anchors) . '<br>';
            $toc .= '</div>';
        }

        return $toc . $content;
    }
    
    public function toc_shortcode($atts = array(), $content = null) {
        $atts = shortcode_atts(array(
            'headings' => implode(',', (array) get_option($this->option_prefix . 'headings', array('h2', 'h3', 'h4')))
        ), $atts);

        $headings = array_filter(
            array_map('trim', explode(',', $atts['headings'])),
            function($h) {
                return in_array(strtolower($h), array('h1', 'h2', 'h3', 'h4', 'h5', 'h6'));
            }
        );

        if (empty($headings)) {
            $headings = array('h2', 'h3', 'h4');
        }

        // Temporarily set headings for this shortcode
        $original_headings = get_option($this->option_prefix . 'headings');
        update_option($this->option_prefix . 'headings', $headings);

        global $post;
        $content = $content ?: (is_a($post, 'WP_Post') ? $post->post_content : '');
        $result = $this->build_toc(apply_filters('the_content', $content));

        // Restore original headings
        update_option($this->option_prefix . 'headings', $original_headings);

        return $result;
    }

    public function check_elementor_compatibility() {
        // Check if Elementor is installed and activated
        if (!did_action('elementor/loaded')) {
            return;
        }

        // Check if we have the minimum required Elementor version
        if (!version_compare(ELEMENTOR_VERSION, '3.0.0', '>=')) {
            return;
        }

        // All good, Elementor is available
        add_action('elementor/init', array($this, 'elementor_init'));
    }

    public function elementor_init() {
        // Add custom category for our widgets
        \Elementor\Plugin::instance()->elements_manager->add_category(
            'safeoid-widgets',
            [
                'title' => __('Safeoid Widgets', SAFEOID_TOC_TEXT_DOMAIN),
                'icon' => 'fa fa-plug',
            ]
        );
    }

    public function register_elementor_widget($widgets_manager) {
        if (!class_exists('Elementor\Widget_Base')) {
            return;
        }

        if (file_exists(SAFEOID_TOC_PLUGIN_DIR . 'widgets/elementor-toc-widget.php')) {
            require_once SAFEOID_TOC_PLUGIN_DIR . 'widgets/elementor-toc-widget.php';

            if (class_exists('SafeoidTOCElementorWidget')) {
                $widgets_manager->register(new SafeoidTOCElementorWidget());

                // Debug: Log successful registration (only for admins)
                if (current_user_can('manage_options') && defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('Safeoid TOC Elementor Widget registered successfully');
                }
            } else {
                // Debug: Log class not found (only for admins)
                if (current_user_can('manage_options') && defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('SafeoidTOCElementorWidget class not found');
                }
            }
        }
    }

    public function register_elementor_widget_legacy($widgets_manager = null) {
        // Legacy method for older Elementor versions
        if (!$widgets_manager) {
            $widgets_manager = \Elementor\Plugin::instance()->widgets_manager;
        }

        $this->register_elementor_widget($widgets_manager);
    }

    public function register_elementor_widget_old() {
        // Very old Elementor compatibility
        if (class_exists('\Elementor\Plugin')) {
            $widgets_manager = \Elementor\Plugin::instance()->widgets_manager;
            if ($widgets_manager) {
                $this->register_elementor_widget($widgets_manager);
            }
        }
    }

    public function elementor_admin_notices() {
        // Only show to admins and only on Elementor pages
        if (!current_user_can('manage_options')) {
            return;
        }

        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'elementor') === false) {
            return;
        }

        // Check Elementor status
        if (!did_action('elementor/loaded')) {
            echo '<div class="notice notice-warning"><p>';
            echo '<strong>Safeoid TOC:</strong> Elementor is not active. The TOC widget will not be available.';
            echo '</p></div>';
            return;
        }

        if (!version_compare(ELEMENTOR_VERSION, '3.0.0', '>=')) {
            echo '<div class="notice notice-warning"><p>';
            echo '<strong>Safeoid TOC:</strong> Elementor version 3.0.0 or higher is required. Current version: ' . ELEMENTOR_VERSION;
            echo '</p></div>';
            return;
        }

        // Check if widget file exists
        if (!file_exists(SAFEOID_TOC_PLUGIN_DIR . 'widgets/elementor-toc-widget.php')) {
            echo '<div class="notice notice-error"><p>';
            echo '<strong>Safeoid TOC:</strong> Widget file not found. Please reinstall the plugin.';
            echo '</p></div>';
            return;
        }

        // Check if widget class exists
        if (!class_exists('SafeoidTOCElementorWidget')) {
            require_once SAFEOID_TOC_PLUGIN_DIR . 'widgets/elementor-toc-widget.php';
            if (!class_exists('SafeoidTOCElementorWidget')) {
                echo '<div class="notice notice-error"><p>';
                echo '<strong>Safeoid TOC:</strong> Widget class not found. Please check the widget file.';
                echo '</p></div>';
                return;
            }
        }

        // If we get here, everything should be working
        if (isset($_GET['safeoid_debug'])) {
            echo '<div class="notice notice-success"><p>';
            echo '<strong>Safeoid TOC:</strong> Elementor integration is working correctly. Look for "Floating TOC" in the Safeoid Widgets or General category.';
            echo '</p></div>';
        }
    }

    public function debug_shortcode($atts) {
        $output = '<div style="background: #f0f0f0; padding: 15px; margin: 10px 0; border-left: 4px solid #0073aa;">';
        $output .= '<h4>Safeoid TOC Debug Information</h4>';

        // Check Elementor
        if (did_action('elementor/loaded')) {
            $output .= '<p>✅ Elementor is loaded (Version: ' . ELEMENTOR_VERSION . ')</p>';
        } else {
            $output .= '<p>❌ Elementor is not loaded</p>';
        }

        // Check widget file
        if (file_exists(SAFEOID_TOC_PLUGIN_DIR . 'widgets/elementor-toc-widget.php')) {
            $output .= '<p>✅ Widget file exists</p>';
        } else {
            $output .= '<p>❌ Widget file not found</p>';
        }

        // Check widget class
        if (!class_exists('SafeoidTOCElementorWidget')) {
            require_once SAFEOID_TOC_PLUGIN_DIR . 'widgets/elementor-toc-widget.php';
        }

        if (class_exists('SafeoidTOCElementorWidget')) {
            $output .= '<p>✅ Widget class exists</p>';
        } else {
            $output .= '<p>❌ Widget class not found</p>';
        }

        // Check if widgets are registered
        if (class_exists('\Elementor\Plugin')) {
            $widgets_manager = \Elementor\Plugin::instance()->widgets_manager;
            $registered_widgets = $widgets_manager->get_widget_types();

            if (isset($registered_widgets['safeoid-toc'])) {
                $output .= '<p>✅ Widget is registered in Elementor</p>';
            } else {
                $output .= '<p>❌ Widget is not registered in Elementor</p>';
                $output .= '<p>Available widgets: ' . implode(', ', array_keys($registered_widgets)) . '</p>';
            }
        }

        $output .= '<p><strong>Instructions:</strong> Look for "Floating TOC" widget in Elementor under "Safeoid Widgets" or "General" category.</p>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Add Rank Math compatibility meta tags
     */
    public function add_rank_math_compatibility() {
        // Add meta tag to indicate TOC presence for Rank Math detection
        if ($this->has_toc_on_page()) {
            echo '<meta name="safeoid-toc-present" content="true">' . "\n";
            echo '<meta name="toc-plugin" content="safeoid-toc">' . "\n";
            echo '<!-- Safeoid TOC Plugin Active -->' . "\n";
        }
    }

    /**
     * Add TOC schema to Rank Math JSON-LD
     */
    public function add_toc_schema($json_ld, $context) {
        if (!$this->has_toc_on_page()) {
            return $json_ld;
        }

        // Add TOC schema to existing JSON-LD
        if (!isset($json_ld['@graph'])) {
            $json_ld['@graph'] = array();
        }

        $toc_schema = array(
            '@type' => 'ItemList',
            '@id' => get_permalink() . '#toc',
            'name' => 'Table of Contents',
            'description' => 'Navigation links for this page content',
            'numberOfItems' => count($this->get_page_headings()),
            'itemListElement' => $this->get_toc_schema_items()
        );

        $json_ld['@graph'][] = $toc_schema;

        return $json_ld;
    }

    /**
     * Check if current page has TOC
     */
    private function has_toc_on_page() {
        global $post;

        if (!$post) {
            return false;
        }

        // Check if auto-insert is enabled and conditions are met
        $auto_insert = get_option($this->option_prefix . 'auto_insert', false);
        if ($auto_insert && $this->should_display_toc()) {
            return true;
        }

        // Check if shortcode is present
        if (has_shortcode($post->post_content, 'safeoid_toc')) {
            return true;
        }

        return false;
    }

    /**
     * Get page headings for schema
     */
    private function get_page_headings() {
        global $post;

        if (!$post) {
            return array();
        }

        $content = apply_filters('the_content', $post->post_content);
        $enabled_headings = get_option($this->option_prefix . 'headings', array('h2', 'h3', 'h4'));

        if (empty($enabled_headings)) {
            return array();
        }

        $heading_pattern = '<(' . implode('|', $enabled_headings) . ')([^>]*?)>(.*?)</\\1>';
        preg_match_all('/' . $heading_pattern . '/is', $content, $matches, PREG_SET_ORDER);

        $headings = array();
        foreach ($matches as $match) {
            $text = wp_strip_all_tags($match[3]);
            if (!empty($text)) {
                $headings[] = array(
                    'text' => $text,
                    'level' => strtolower($match[1]),
                    'id' => $this->generate_heading_id($text)
                );
            }
        }

        return $headings;
    }

    /**
     * Get TOC schema items
     */
    private function get_toc_schema_items() {
        $headings = $this->get_page_headings();
        $items = array();

        foreach ($headings as $index => $heading) {
            $items[] = array(
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $heading['text'],
                'url' => get_permalink() . '#' . $heading['id']
            );
        }

        return $items;
    }

    /**
     * Register plugin with Rank Math TOC detection
     * This is the CRITICAL method that tells Rank Math our plugin exists
     */
    public function register_with_rank_math($toc_plugins) {
        // Add our plugin to the list of recognized TOC plugins
        $toc_plugins[SAFEOID_TOC_PLUGIN_FILE] = 'Safeoid TOC';

        return $toc_plugins;
    }

    /**
     * Alternative method to extract headings if main regex fails
     */
    private function extract_headings_alternative($content, $headings) {
        $matches = array();

        // Try with DOMDocument for more reliable parsing
        if (class_exists('DOMDocument')) {
            $dom = new DOMDocument();

            // Suppress warnings for malformed HTML
            libxml_use_internal_errors(true);

            // Load content with UTF-8 encoding
            $dom->loadHTML('<?xml encoding="UTF-8">' . $content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

            // Clear errors
            libxml_clear_errors();

            foreach ($headings as $heading_tag) {
                $heading_elements = $dom->getElementsByTagName($heading_tag);

                foreach ($heading_elements as $element) {
                    $text_content = trim($element->textContent);
                    if (!empty($text_content)) {
                        // Create match array in same format as preg_match_all
                        $attributes = '';
                        if ($element->hasAttributes()) {
                            foreach ($element->attributes as $attr) {
                                $attributes .= ' ' . $attr->nodeName . '="' . $attr->nodeValue . '"';
                            }
                        }

                        $matches[] = array(
                            0 => $element->ownerDocument->saveHTML($element), // Full match
                            1 => $heading_tag, // Tag name
                            2 => $attributes, // Attributes
                            3 => $text_content // Text content
                        );
                    }
                }
            }
        }

        // Fallback: Try simpler regex patterns
        if (empty($matches)) {
            foreach ($headings as $heading_tag) {
                $simple_pattern = '/<' . $heading_tag . '[^>]*>(.*?)<\/' . $heading_tag . '>/is';
                preg_match_all($simple_pattern, $content, $simple_matches, PREG_SET_ORDER);

                foreach ($simple_matches as $match) {
                    $text = wp_strip_all_tags($match[1]);
                    if (!empty(trim($text))) {
                        $matches[] = array(
                            0 => $match[0],
                            1 => $heading_tag,
                            2 => '', // No attributes in simple pattern
                            3 => $text
                        );
                    }
                }
            }
        }

        return $matches;
    }

    /**
     * Generate a clean ID from heading text
     */
    private function generate_heading_id($text) {
        // Remove HTML tags
        $text = wp_strip_all_tags($text);

        // Convert to lowercase and replace spaces with hyphens
        $id = strtolower(trim($text));
        $id = preg_replace('/[^a-z0-9\-_]/', '', str_replace(' ', '-', $id));

        // Remove multiple consecutive hyphens
        $id = preg_replace('/-+/', '-', $id);

        // Remove leading/trailing hyphens
        $id = trim($id, '-');

        // Ensure ID is not empty
        if (empty($id)) {
            $id = 'heading-' . uniqid();
        }

        return $id;
    }

}

// Initialize the plugin
SafeoidTOC::get_instance();

} // End class_exists check
