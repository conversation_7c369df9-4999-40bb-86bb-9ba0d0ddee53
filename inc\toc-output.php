<?php
/**
 * TOC Output Class
 * Generates and renders the Table of Contents HTML
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SafeoidTOCOutput {
    
    private $settings;
    private $headings = array();
    
    public function __construct() {
        $this->settings = get_option('safeoid_toc_settings', array());
        $this->set_defaults();
    }
    
    /**
     * Set default settings
     */
    private function set_defaults() {
        $defaults = array(
            'headings' => array('h2', 'h3', 'h4'),
            'toc_label' => __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN),
            'enable_desktop' => true,
            'enable_mobile' => true,
            'text_color' => '#333333',
            'link_color' => '#0073aa',
            'background_color' => '#ffffff',
            'disable_schema' => false
        );
        
        $this->settings = wp_parse_args($this->settings, $defaults);
    }
    
    /**
     * Render the TOC
     */
    public function render() {
        if (!$this->should_display_toc()) {
            return;
        }

        $this->extract_headings();

        // Debug output for administrators
        if (current_user_can('manage_options') && isset($_GET['toc_debug'])) {
            $this->debug_output();
        }

        if (empty($this->headings)) {
            // Show debug info for admins even when no headings found
            if (current_user_can('manage_options') && isset($_GET['toc_debug'])) {
                echo '<div style="background: #ffeeee; border: 1px solid #ff0000; padding: 15px; margin: 20px 0;">';
                echo '<h3>TOC Debug: No headings found</h3>';
                echo '<p>Add ?toc_debug=1 to the URL to see debug information.</p>';
                echo '</div>';
            }
            return;
        }

        $this->output_css_variables();
        $this->output_unified_toc();
    }

    /**
     * Generate heading ID from content
     */
    private function generate_heading_id($content) {
        // Remove HTML tags
        $text = wp_strip_all_tags($content);

        // Convert to lowercase and replace spaces with hyphens
        $id = strtolower(trim($text));
        $id = preg_replace('/[^a-z0-9\-_]/', '', str_replace(' ', '-', $id));

        // Remove multiple consecutive hyphens
        $id = preg_replace('/-+/', '-', $id);

        // Remove leading/trailing hyphens
        $id = trim($id, '-');

        // Ensure ID is not empty
        if (empty($id)) {
            $id = 'heading-' . uniqid();
        }

        return $id;
    }
    
    /**
     * Check if TOC should be displayed
     */
    private function should_display_toc() {
        // Don't show on admin pages
        if (is_admin()) {
            return false;
        }
        
        // Only show on posts and pages by default
        if (!is_single() && !is_page()) {
            return false;
        }
        
        // Check if at least one display option is enabled
        if (!$this->settings['enable_desktop'] && !$this->settings['enable_mobile']) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Extract headings from current post content
     */
    private function extract_headings() {
        global $post;

        if (!$post || empty($post->post_content)) {
            return;
        }

        // Get the processed content (this should include our added IDs)
        $content = apply_filters('the_content', $post->post_content);
        $enabled_headings = $this->settings['headings'];

        if (empty($enabled_headings)) {
            return;
        }

        // Create a more comprehensive pattern to match all headings
        $heading_pattern = '<(' . implode('|', $enabled_headings) . ')([^>]*?)>(.*?)</\\1>';
        preg_match_all('/' . $heading_pattern . '/is', $content, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $level = strtolower($match[1]);
            $attributes = $match[2];
            $text = wp_strip_all_tags($match[3]);

            if (empty($text)) {
                continue;
            }

            // Extract ID from attributes if it exists
            $id = '';
            if (preg_match('/id=["\']([^"\']*)["\']/', $attributes, $id_match)) {
                $id = $id_match[1];
            } else {
                // Generate ID if none exists
                $id = $this->generate_heading_id($text);
            }

            if (!empty($id) && !empty($text)) {
                $this->headings[] = array(
                    'level' => $level,
                    'id' => $id,
                    'text' => $text,
                    'depth' => (int) substr($level, 1)
                );
            }
        }

        // Remove duplicates based on ID
        $unique_headings = array();
        $seen_ids = array();

        foreach ($this->headings as $heading) {
            if (!in_array($heading['id'], $seen_ids)) {
                $unique_headings[] = $heading;
                $seen_ids[] = $heading['id'];
            }
        }

        $this->headings = $unique_headings;
    }
    
    /**
     * Output CSS custom properties for colors
     */
    private function output_css_variables() {
        echo '<style>';
        echo ':root {';
        echo '--safeoid-toc-text-color: ' . esc_attr($this->settings['text_color']) . ';';
        echo '--safeoid-toc-link-color: ' . esc_attr($this->settings['link_color']) . ';';
        echo '--safeoid-toc-bg-color: ' . esc_attr($this->settings['background_color']) . ';';
        echo '--safeoid-toc-link-hover-color: ' . esc_attr($this->adjust_color_brightness($this->settings['link_color'], 20)) . ';';
        echo '}';
        echo '</style>';
    }
    
    /**
     * Output unified TOC (works for both desktop and mobile)
     */
    private function output_unified_toc() {
        if (!$this->settings['enable_desktop'] && !$this->settings['enable_mobile']) {
            return;
        }

        echo '<div class="floating-toc">';
        echo '<h3 class="toc-heading">' . esc_html($this->settings['toc_label']) . '</h3>';
        echo '<div class="toc-drag-handle" title="Drag to move TOC"></div>';
        echo '<div class="toc-toggle">';
        echo '☰ ' . esc_html($this->settings['toc_label']);
        echo '</div>';
        echo '<div class="toc-content">';
        $this->output_toc_list();
        echo '</div>';
        echo '</div>';
    }
    
    /**
     * Output the TOC list structure
     */
    private function output_toc_list() {
        if (empty($this->headings)) {
            return;
        }
        
        $current_depth = 0;
        $list_stack = array();
        
        echo '<ul>';
        $current_depth = $this->headings[0]['depth'];
        
        foreach ($this->headings as $heading) {
            $depth = $heading['depth'];
            
            // Handle depth changes
            if ($depth > $current_depth) {
                // Going deeper - open new nested list
                for ($i = $current_depth; $i < $depth; $i++) {
                    echo '<ul>';
                    array_push($list_stack, $i);
                }
            } elseif ($depth < $current_depth) {
                // Going shallower - close nested lists
                for ($i = $current_depth; $i > $depth; $i--) {
                    echo '</ul>';
                    array_pop($list_stack);
                }
            }
            
            // Output the list item
            echo '<li>';
            echo '<a href="#' . esc_attr($heading['id']) . '">';
            echo esc_html($heading['text']);
            echo '</a>';
            echo '</li>';
            
            $current_depth = $depth;
        }
        
        // Close any remaining open lists
        while (!empty($list_stack)) {
            echo '</ul>';
            array_pop($list_stack);
        }
        
        echo '</ul>';
    }
    
    /**
     * Adjust color brightness
     */
    private function adjust_color_brightness($hex, $percent) {
        // Remove # if present
        $hex = ltrim($hex, '#');
        
        // Convert to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Adjust brightness
        $r = max(0, min(255, $r + ($percent * 255 / 100)));
        $g = max(0, min(255, $g + ($percent * 255 / 100)));
        $b = max(0, min(255, $b + ($percent * 255 / 100)));
        
        // Convert back to hex
        return '#' . sprintf('%02x%02x%02x', $r, $g, $b);
    }
    
    /**
     * Get headings for external use (e.g., schema, Elementor widget)
     */
    public function get_headings() {
        if (empty($this->headings)) {
            $this->extract_headings();
        }
        return $this->headings;
    }

    /**
     * Debug method to output current post content and found headings
     * Only visible to administrators
     */
    public function debug_output() {
        if (!current_user_can('manage_options')) {
            return;
        }

        global $post;

        echo '<div style="background: #f5f5f5; border: 1px solid #ddd; padding: 15px; margin: 20px 0; font-family: monospace;">';
        echo '<h3>TOC Debug Information (Admin Only)</h3>';

        echo '<h4>Found Headings:</h4>';
        if (empty($this->headings)) {
            echo '<p>No headings found.</p>';
        } else {
            echo '<ul>';
            foreach ($this->headings as $heading) {
                echo '<li>';
                echo '<strong>Level:</strong> ' . esc_html($heading['level']) . ', ';
                echo '<strong>ID:</strong> ' . esc_html($heading['id']) . ', ';
                echo '<strong>Text:</strong> ' . esc_html($heading['text']);
                echo '</li>';
            }
            echo '</ul>';
        }

        echo '<h4>Current Settings:</h4>';
        echo '<pre>' . print_r($this->settings, true) . '</pre>';

        echo '</div>';
    }
}
