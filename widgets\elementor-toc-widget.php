<?php
/**
 * Elementor TOC Widget
 * Custom Elementor widget for displaying the Table of Contents
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Safeoid TOC Elementor Widget
 */
class SafeoidTOCElementorWidget extends \Elementor\Widget_Base {
    
    /**
     * Get widget name
     */
    public function get_name() {
        return 'safeoid_toc';
    }
    
    /**
     * Get widget title
     */
    public function get_title() {
        return __('Floating TOC', SAFEOID_TOC_TEXT_DOMAIN);
    }
    
    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-table-of-contents';
    }
    
    /**
     * Get widget categories
     */
    public function get_categories() {
        return ['general'];
    }
    
    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return ['toc', 'table of contents', 'navigation', 'safeoid'];
    }
    
    /**
     * Register widget controls
     */
    protected function _register_controls() {
        $this->start_controls_section(
            'section_content',
            [
                'label' => __('TOC Settings', SAFEOID_TOC_TEXT_DOMAIN),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );
        
        $this->add_control(
            'toc_title',
            [
                'label' => __('TOC Title', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN),
                'placeholder' => __('Enter your title', SAFEOID_TOC_TEXT_DOMAIN),
            ]
        );
        
        $this->add_control(
            'headings',
            [
                'label' => __('Include Headings', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'multiple' => true,
                'options' => [
                    'h1' => 'H1',
                    'h2' => 'H2',
                    'h3' => 'H3',
                    'h4' => 'H4',
                    'h5' => 'H5',
                    'h6' => 'H6',
                ],
                'default' => ['h2', 'h3', 'h4'],
            ]
        );
        
        $this->add_control(
            'use_global_settings',
            [
                'label' => __('Use Global Settings', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', SAFEOID_TOC_TEXT_DOMAIN),
                'label_off' => __('No', SAFEOID_TOC_TEXT_DOMAIN),
                'return_value' => 'yes',
                'default' => 'no', // Changed from 'yes' to 'no'
            ]
        );
        
        $this->end_controls_section();
        
        $this->start_controls_section(
            'section_style',
            [
                'label' => __('TOC Style', SAFEOID_TOC_TEXT_DOMAIN),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'use_global_settings' => '',
                ],
            ]
        );
        
        $this->add_control(
            'text_color',
            [
                'label' => __('Text Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc' => 'color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_control(
            'link_color',
            [
                'label' => __('Link Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#0073aa',
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc a' => 'color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_control(
            'background_color',
            [
                'label' => __('Background Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc' => 'background-color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'border',
                'selector' => '{{WRAPPER}} .floating-toc',
                'separator' => 'before',
            ]
        );
        
        $this->add_control(
            'border_radius',
            [
                'label' => __('Border Radius', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .floating-toc' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'box_shadow',
                'selector' => '{{WRAPPER}} .safeoid-toc',
            ]
        );
        
        $this->add_responsive_control(
            'padding',
            [
                'label' => __('Padding', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .floating-toc' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '20',
                    'right' => '20',
                    'bottom' => '20',
                    'left' => '20',
                    'unit' => 'px',
                    'isLinked' => true,
                ],
            ]
        );

        $this->end_controls_section();

        // Typography Section
        $this->start_controls_section(
            'section_typography',
            [
                'label' => __('Typography', SAFEOID_TOC_TEXT_DOMAIN),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'heading_typography',
                'label' => __('Heading Typography', SAFEOID_TOC_TEXT_DOMAIN),
                'selector' => '{{WRAPPER}} .floating-toc .toc-heading',
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'links_typography',
                'label' => __('Links Typography', SAFEOID_TOC_TEXT_DOMAIN),
                'selector' => '{{WRAPPER}} .floating-toc a',
            ]
        );

        $this->add_control(
            'heading_color',
            [
                'label' => __('Heading Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .floating-toc .toc-heading' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'links_color',
            [
                'label' => __('Links Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .floating-toc a' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'links_hover_color',
            [
                'label' => __('Links Hover Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .floating-toc a:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'links_active_color',
            [
                'label' => __('Active Link Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .floating-toc a.active' => 'color: {{VALUE}}; font-weight: bold;',
                ],
            ]
        );

        $this->end_controls_section();

        // Position Controls Section
        $this->start_controls_section(
            'section_position',
            [
                'label' => __('Position', SAFEOID_TOC_TEXT_DOMAIN),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_responsive_control(
            'top_offset',
            [
                'label' => __('Top Offset', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'vh', '%'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 500,
                        'step' => 1,
                    ],
                    'vh' => [
                        'min' => 0,
                        'max' => 100,
                        'step' => 1,
                    ],
                    '%' => [
                        'min' => 0,
                        'max' => 100,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 60,
                ],
                'selectors' => [
                    '{{WRAPPER}} .floating-toc' => 'top: {{SIZE}}{{UNIT}};',
                ],
                'description' => __('Adjust the distance from the top of the viewport. Default is 60px to account for admin bars and headers.', SAFEOID_TOC_TEXT_DOMAIN),
            ]
        );

        $this->add_responsive_control(
            'left_offset',
            [
                'label' => __('Left Offset', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'vw', '%'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 200,
                        'step' => 1,
                    ],
                    'vw' => [
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ],
                    '%' => [
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 20,
                ],
                'selectors' => [
                    '{{WRAPPER}} .floating-toc' => 'left: {{SIZE}}{{UNIT}};',
                ],
                'description' => __('Adjust the distance from the left edge of the viewport.', SAFEOID_TOC_TEXT_DOMAIN),
            ]
        );

        $this->end_controls_section();
    }
    
    /**
     * Render widget output
     */
    protected function render() {
        $settings = $this->get_settings_for_display();

        // Get global settings if enabled
        $use_global = $settings['use_global_settings'] === 'yes';

        // Determine which settings to use
        if ($use_global) {
            $toc_title = get_option('safeoid_toc_toc_label', __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN));
            $headings = get_option('safeoid_toc_headings', array('h2', 'h3', 'h4'));
        } else {
            $toc_title = $settings['toc_title'];
            $headings = $settings['headings'];
        }

        // Get the main plugin instance
        $plugin = SafeoidTOC::get_instance();

        // Get current post content
        global $post;
        $content = $post ? apply_filters('the_content', $post->post_content) : '';

        if (empty($content)) {
            echo '<div class="floating-toc elementor-widget">';
            echo '<h3 class="toc-heading">' . esc_html($toc_title) . '</h3>';
            echo '<div class="toc-content">';
            echo '<p>' . __('No content found to generate TOC.', SAFEOID_TOC_TEXT_DOMAIN) . '</p>';
            echo '</div>';
            echo '</div>';
            return;
        }

        // Temporarily set headings for this widget
        $original_headings = get_option('safeoid_toc_headings');
        update_option('safeoid_toc_headings', $headings);

        // Generate TOC
        $toc_html = $plugin->build_toc($content);

        // Restore original headings
        update_option('safeoid_toc_headings', $original_headings);

        // Extract just the TOC part (remove the content)
        if (preg_match('/<div class="floating-toc".*?<\/div><\/div>/s', $toc_html, $matches)) {
            echo $matches[0];
        } else {
            echo '<div class="floating-toc elementor-widget">';
            echo '<h3 class="toc-heading">' . esc_html($toc_title) . '</h3>';
            echo '<div class="toc-content">';
            echo '<p>' . __('No headings found in the content.', SAFEOID_TOC_TEXT_DOMAIN) . '</p>';
            echo '</div>';
            echo '</div>';
        }
    }
}
