<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced TOC Test</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1, h2, h3, h4 {
            color: #333;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        
        h1 {
            border-bottom: 3px solid #0073e6;
            padding-bottom: 10px;
        }
        
        p {
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .test-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 10000;
        }
        
        .test-controls button {
            display: block;
            width: 100%;
            margin: 5px 0;
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: #f9f9f9;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .test-controls button:hover {
            background: #e9e9e9;
        }
    </style>
</head>
<body>
    <!-- Test Controls -->
    <div class="test-controls">
        <h4>Test Controls</h4>
        <button onclick="testScrollCollapse()">Test Scroll Collapse</button>
        <button onclick="testRepeatedCollapse()">Test Repeated Collapse</button>
        <button onclick="testToggleExpand()">Test Toggle Expand</button>
        <button onclick="testTOCClicking()">Test TOC Clicking</button>
        <button onclick="testMobileView()">Test Mobile View</button>
        <button onclick="resetTOC()">Reset TOC</button>
    </div>

    <!-- Enhanced TOC -->
    <div class="floating-toc">
        <h3 class="toc-heading">Table of Contents</h3>
        <div class="toc-toggle">☰ Table of Contents</div>
        <div class="toc-content">
            <ul>
                <li><a href="#introduction">Introduction</a></li>
                <li><a href="#features">Features</a>
                    <ul>
                        <li><a href="#auto-collapse">Auto-Collapse on Scroll</a></li>
                        <li><a href="#padding-control">Padding Control</a></li>
                        <li><a href="#click-functionality">Click Functionality</a></li>
                    </ul>
                </li>
                <li><a href="#implementation">Implementation</a>
                    <ul>
                        <li><a href="#css-changes">CSS Changes</a></li>
                        <li><a href="#javascript-changes">JavaScript Changes</a></li>
                    </ul>
                </li>
                <li><a href="#testing">Testing</a></li>
                <li><a href="#conclusion">Conclusion</a></li>
            </ul>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content">
        <h1 id="introduction">Introduction</h1>
        <p>This is a test page for the enhanced Table of Contents plugin. The TOC now includes several new features including auto-collapse on scroll, adjustable padding through Elementor controls, improved click functionality, and better mobile responsiveness.</p>
        
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>

        <h2 id="features">Features</h2>
        <p>The enhanced TOC includes several key improvements that make it more user-friendly and functional across different devices and screen sizes.</p>

        <h3 id="auto-collapse">Auto-Collapse on Scroll</h3>
        <p>When users scroll the page, the TOC automatically collapses to save screen space. It shows a compact version with just the title and a dropdown arrow. Users can click on the collapsed TOC to expand it again.</p>
        
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>

        <h3 id="padding-control">Padding Control</h3>
        <p>The Elementor widget now includes controls for adjusting the top and left padding of the TOC. This allows users to position the TOC correctly relative to their site's header and navigation elements.</p>
        
        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>

        <h3 id="click-functionality">Click Functionality</h3>
        <p>The TOC links now work more reliably with improved smooth scrolling and proper handling of fixed headers. Active links are highlighted to show the current section.</p>
        
        <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>

        <h2 id="implementation">Implementation</h2>
        <p>The implementation involved changes to both CSS and JavaScript files to support the new functionality while maintaining backward compatibility.</p>

        <h3 id="css-changes">CSS Changes</h3>
        <p>New CSS classes were added to handle the collapsed state, animations, and responsive behavior. The styles ensure smooth transitions and proper display across different screen sizes.</p>
        
        <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>

        <h3 id="javascript-changes">JavaScript Changes</h3>
        <p>The JavaScript was enhanced with scroll detection, toggle functionality, and improved event handling. Event delegation is used to ensure links work even when the TOC is dynamically updated.</p>
        
        <p>Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.</p>

        <h2 id="testing">Testing</h2>
        <p>Use the test controls in the top-right corner to verify the functionality. Test the auto-collapse by scrolling, the toggle functionality by clicking on the collapsed TOC, and the mobile view by resizing your browser window.</p>
        
        <p>Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.</p>

        <h2 id="conclusion">Conclusion</h2>
        <p>The enhanced TOC provides a better user experience with improved functionality and responsiveness. All features work together to create a seamless navigation experience for website visitors.</p>
        
        <p>Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae. Itaque earum rerum hic tenetur a sapiente delectus.</p>
    </div>

    <script src="js/toc.js"></script>
    <script>
        // Test functions
        function testScrollCollapse() {
            if (window.innerWidth <= 1024) {
                alert('Auto-collapse is disabled on mobile. Mobile uses original sticky header behavior.');
                return;
            }

            alert('Testing auto-collapse: Will scroll down, then up, then down again. TOC should collapse each time.');

            // First scroll down
            window.scrollTo({ top: 500, behavior: 'smooth' });

            setTimeout(() => {
                // Scroll back up
                window.scrollTo({ top: 100, behavior: 'smooth' });

                setTimeout(() => {
                    // Scroll down again
                    window.scrollTo({ top: 800, behavior: 'smooth' });

                    setTimeout(() => {
                        alert('Test complete! TOC should have collapsed multiple times. It will auto-expand in 2 seconds after scrolling stops.');
                    }, 1000);
                }, 1500);
            }, 1500);
        }

        function testToggleExpand() {
            const toc = document.querySelector('.floating-toc');
            if (toc) {
                if (window.innerWidth <= 1024) {
                    alert('On mobile, click the hamburger menu (☰) to toggle TOC content.');
                } else {
                    toc.classList.add('collapsed');
                    setTimeout(() => {
                        alert('TOC is now collapsed. Click on the TOC heading to expand it.');
                    }, 500);
                }
            }
        }

        function testMobileView() {
            document.body.style.width = '400px';
            setTimeout(() => {
                alert('Mobile view: TOC should be sticky at top. Auto-collapse is disabled. Use hamburger menu to toggle.');
            }, 500);
        }

        function resetTOC() {
            const toc = document.querySelector('.floating-toc');
            if (toc) {
                toc.classList.remove('collapsed', 'expanded');
                document.body.classList.remove('toc-open');
                document.body.style.width = '';
                window.scrollTo({ top: 0, behavior: 'smooth' });
                alert('TOC reset to initial state.');
            }
        }

        function testRepeatedCollapse() {
            if (window.innerWidth <= 1024) {
                alert('Auto-collapse is disabled on mobile.');
                return;
            }

            let scrollCount = 0;
            const maxScrolls = 5;

            function doScroll() {
                scrollCount++;
                const scrollPosition = scrollCount % 2 === 1 ? 400 * scrollCount : 100;

                console.log(`Scroll ${scrollCount}: Moving to ${scrollPosition}px`);
                window.scrollTo({ top: scrollPosition, behavior: 'smooth' });

                if (scrollCount < maxScrolls) {
                    setTimeout(doScroll, 2000);
                } else {
                    setTimeout(() => {
                        alert('Repeated collapse test complete! TOC should have collapsed each time you scrolled.');
                    }, 1000);
                }
            }

            alert('Testing repeated auto-collapse. Will scroll 5 times. Watch the TOC collapse each time.');
            doScroll();
        }

        function testTOCClicking() {
            alert('Click on any TOC link. It should scroll smoothly WITHOUT collapsing the TOC.');
        }
    </script>
</body>
</html>
