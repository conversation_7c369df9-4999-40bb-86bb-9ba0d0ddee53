/**
 * Safeoid TOC JavaScript
 * Simple vanilla JS for TOC functionality
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTOC);
    } else {
        initTOC();
    }

    function initTOC() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Add body class for browsers that don't support :has()
        document.body.classList.add('has-floating-toc');

        // Initialize smooth scrolling
        initSmoothScrolling();

        // Initialize auto-collapse functionality
        initAutoCollapse();

        // Initialize toggle functionality
        initToggleFunctionality();
    }

    /**
     * Initialize smooth scrolling for TOC links
     */
    function initSmoothScrolling() {
        // Use event delegation to handle dynamically added links
        document.addEventListener('click', function(e) {
            // Check if clicked element is a TOC link
            const link = e.target.closest('.floating-toc a[href^="#"]');
            if (!link) return;

            e.preventDefault();
            e.stopPropagation();

            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const offsetTop = getElementOffsetTop(targetElement);
                const scrollOffset = getScrollOffset();

                // Smooth scroll to target
                window.scrollTo({
                    top: Math.max(0, offsetTop - scrollOffset),
                    behavior: 'smooth'
                });

                // Handle TOC state after clicking
                const tocElement = document.querySelector('.floating-toc');
                if (tocElement) {
                    // Close mobile TOC after clicking
                    if (window.innerWidth <= 1024) {
                        document.body.classList.remove('toc-open');
                        tocElement.classList.remove('expanded');
                    } else {
                        // On desktop, collapse the TOC after clicking if it was expanded
                        if (tocElement.classList.contains('expanded')) {
                            tocElement.classList.remove('expanded');
                            tocElement.classList.add('collapsed');
                        }
                    }
                }

                // Add active state to clicked link
                const allTocLinks = document.querySelectorAll('.floating-toc a[href^="#"]');
                allTocLinks.forEach(function(tocLink) {
                    tocLink.classList.remove('active');
                });
                link.classList.add('active');
            }
        });
    }

    /**
     * Get element's offset from top of page
     */
    function getElementOffsetTop(element) {
        let offsetTop = 0;
        while (element) {
            offsetTop += element.offsetTop;
            element = element.offsetParent;
        }
        return offsetTop;
    }

    /**
     * Get scroll offset for fixed headers
     */
    function getScrollOffset() {
        let offset = 20; // Default offset

        // WordPress admin bar
        const adminBar = document.getElementById('wpadminbar');
        if (adminBar) {
            offset += adminBar.offsetHeight;
        }

        // Mobile TOC
        const mobileToc = document.querySelector('.floating-toc');
        if (mobileToc && window.innerWidth <= 1024) {
            offset += 60; // Mobile TOC height
        }

        // Check for common theme headers
        const stickyHeaders = document.querySelectorAll('.sticky-header, .fixed-header, header.fixed');
        stickyHeaders.forEach(function(header) {
            if (window.getComputedStyle(header).position === 'fixed') {
                offset += header.offsetHeight;
            }
        });

        return offset;
    }

    /**
     * Initialize auto-collapse functionality
     */
    function initAutoCollapse() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Skip auto-collapse on mobile (handled differently)
        if (window.innerWidth <= 1024) return;

        let lastScrollTop = 0;
        let scrollTimeout;
        let isUserScrolling = false;

        // Initially TOC is open
        tocElement.classList.remove('collapsed');

        function handleScroll() {
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Detect if user is scrolling
            if (Math.abs(currentScrollTop - lastScrollTop) > 5) {
                isUserScrolling = true;

                // Collapse TOC when scrolling (but not if it's already expanded manually)
                if (!tocElement.classList.contains('expanded')) {
                    tocElement.classList.add('collapsed');
                }

                // Clear existing timeout
                clearTimeout(scrollTimeout);

                // Set timeout to detect when scrolling stops
                scrollTimeout = setTimeout(() => {
                    isUserScrolling = false;
                }, 150);
            }

            lastScrollTop = currentScrollTop;
        }

        // Throttle scroll events
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', onScroll);

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 1024) {
                tocElement.classList.remove('collapsed', 'expanded');
            }
        });
    }

    /**
     * Initialize toggle functionality for collapsed state
     */
    function initToggleFunctionality() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        const tocHeading = tocElement.querySelector('.toc-heading');
        const tocToggle = tocElement.querySelector('.toc-toggle');

        // Desktop: Click on heading when collapsed
        if (tocHeading) {
            tocHeading.addEventListener('click', function() {
                if (window.innerWidth > 1024 && tocElement.classList.contains('collapsed')) {
                    tocElement.classList.toggle('expanded');
                }
            });
        }

        // Mobile: Click on toggle
        if (tocToggle) {
            tocToggle.addEventListener('click', function() {
                if (window.innerWidth <= 1024) {
                    document.body.classList.toggle('toc-open');
                    tocElement.classList.toggle('expanded');
                } else if (tocElement.classList.contains('collapsed')) {
                    tocElement.classList.toggle('expanded');
                }
            });
        }
    }

    /**
     * Future feature: Scroll spy to highlight current section
     * Uncomment and customize as needed
     */
    /*
    function initScrollSpy() {
        const tocLinks = document.querySelectorAll('.safeoid-toc a[href^="#"]');
        const headings = [];
        
        tocLinks.forEach(function(link) {
            const targetId = link.getAttribute('href').substring(1);
            const heading = document.getElementById(targetId);
            if (heading) {
                headings.push({
                    element: heading,
                    link: link,
                    top: getElementOffsetTop(heading)
                });
            }
        });
        
        if (headings.length === 0) return;
        
        function updateActiveLink() {
            const scrollTop = window.pageYOffset;
            const scrollOffset = getScrollOffset();
            
            let activeHeading = headings[0];
            
            for (let i = 0; i < headings.length; i++) {
                if (scrollTop >= headings[i].top - scrollOffset - 50) {
                    activeHeading = headings[i];
                }
            }
            
            // Remove active class from all links
            tocLinks.forEach(function(link) {
                link.classList.remove('active');
            });
            
            // Add active class to current link
            if (activeHeading) {
                activeHeading.link.classList.add('active');
            }
        }
        
        // Throttle scroll events
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    updateActiveLink();
                    ticking = false;
                });
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', onScroll);
        updateActiveLink(); // Initial call
    }
    */
    
})();
