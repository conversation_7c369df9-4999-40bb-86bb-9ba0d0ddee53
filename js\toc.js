/**
 * Safeoid TOC JavaScript
 * Simple vanilla JS for TOC functionality
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTOC);
    } else {
        initTOC();
    }

    function initTOC() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Add body class for browsers that don't support :has()
        document.body.classList.add('has-floating-toc');

        // Initialize smooth scrolling
        initSmoothScrolling();

        // Initialize auto-collapse functionality
        initAutoCollapse();

        // Initialize toggle functionality
        initToggleFunctionality();

        // Initialize drag functionality
        initDragFunctionality();
    }

    /**
     * Initialize smooth scrolling for TOC links
     */
    function initSmoothScrolling() {
        // Use event delegation to handle dynamically added links
        document.addEventListener('click', function(e) {
            // Check if clicked element is a TOC link
            const link = e.target.closest('.floating-toc a[href^="#"]');
            if (!link) return;

            e.preventDefault();
            e.stopPropagation();

            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                // Set flag to prevent auto-collapse during click scrolling
                if (window.setTOCClickScrolling) {
                    window.setTOCClickScrolling(true);
                }

                const offsetTop = getElementOffsetTop(targetElement);
                const scrollOffset = getScrollOffset();

                // Smooth scroll to target
                window.scrollTo({
                    top: Math.max(0, offsetTop - scrollOffset),
                    behavior: 'smooth'
                });

                // Handle TOC state after clicking
                const tocElement = document.querySelector('.floating-toc');
                if (tocElement) {
                    // Close mobile TOC after clicking (keep original mobile behavior)
                    if (window.innerWidth <= 1024) {
                        document.body.classList.remove('toc-open');
                    } else {
                        // On desktop, DON'T collapse the TOC when clicking items
                        // Keep it in whatever state it was (open or expanded)
                    }
                }

                // Add active state to clicked link
                const allTocLinks = document.querySelectorAll('.floating-toc a[href^="#"]');
                allTocLinks.forEach(function(tocLink) {
                    tocLink.classList.remove('active');
                });
                link.classList.add('active');
            }
        });
    }

    /**
     * Get element's offset from top of page
     */
    function getElementOffsetTop(element) {
        let offsetTop = 0;
        while (element) {
            offsetTop += element.offsetTop;
            element = element.offsetParent;
        }
        return offsetTop;
    }

    /**
     * Get scroll offset for fixed headers
     */
    function getScrollOffset() {
        let offset = 20; // Default offset

        // WordPress admin bar
        const adminBar = document.getElementById('wpadminbar');
        if (adminBar) {
            offset += adminBar.offsetHeight;
        }

        // Mobile TOC
        const mobileToc = document.querySelector('.floating-toc');
        if (mobileToc && window.innerWidth <= 1024) {
            offset += 60; // Mobile TOC height
        }

        // Check for common theme headers
        const stickyHeaders = document.querySelectorAll('.sticky-header, .fixed-header, header.fixed');
        stickyHeaders.forEach(function(header) {
            if (window.getComputedStyle(header).position === 'fixed') {
                offset += header.offsetHeight;
            }
        });

        return offset;
    }

    /**
     * Initialize auto-collapse functionality
     */
    function initAutoCollapse() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        let lastScrollTop = 0;
        let scrollTimeout;
        let isUserScrolling = false;
        let isClickScrolling = false; // Flag to prevent collapse during click scrolling

        // Initially TOC is open on desktop
        if (window.innerWidth > 1024) {
            tocElement.classList.remove('collapsed');
        }

        function handleScroll() {
            // Skip auto-collapse on mobile
            if (window.innerWidth <= 1024) return;

            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Don't collapse if this is a programmatic scroll from clicking TOC links
            if (isClickScrolling) return;

            // Detect if user is manually scrolling
            if (Math.abs(currentScrollTop - lastScrollTop) > 10) {
                isUserScrolling = true;

                // Always collapse when scrolling, regardless of expanded state
                // Remove expanded state and add collapsed state
                tocElement.classList.remove('expanded');
                tocElement.classList.add('collapsed');

                // Clear existing timeout
                clearTimeout(scrollTimeout);

                // Set timeout to detect when scrolling stops
                scrollTimeout = setTimeout(() => {
                    isUserScrolling = false;
                }, 200);
            }

            lastScrollTop = currentScrollTop;
        }

        // Throttle scroll events
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', onScroll);

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 1024) {
                tocElement.classList.remove('collapsed', 'expanded');
            } else {
                // Reset to open state on desktop
                tocElement.classList.remove('collapsed');
            }
        });

        // Expose function to set click scrolling flag
        window.setTOCClickScrolling = function(value) {
            isClickScrolling = value;
            if (value) {
                // Clear the flag after a delay
                setTimeout(() => {
                    isClickScrolling = false;
                }, 1500);
            }
        };
    }

    /**
     * Initialize toggle functionality for collapsed state
     */
    function initToggleFunctionality() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        const tocHeading = tocElement.querySelector('.toc-heading');
        const tocToggle = tocElement.querySelector('.toc-toggle');

        // Desktop: Click on heading when collapsed
        if (tocHeading) {
            tocHeading.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                if (window.innerWidth > 1024 && tocElement.classList.contains('collapsed')) {
                    tocElement.classList.toggle('expanded');
                }
            });
        }

        // Mobile: Click on toggle (keep original mobile behavior)
        if (tocToggle) {
            tocToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                if (window.innerWidth <= 1024) {
                    // Original mobile behavior - just toggle toc-open class
                    document.body.classList.toggle('toc-open');
                } else {
                    // Desktop: toggle expanded state when collapsed
                    if (tocElement.classList.contains('collapsed')) {
                        tocElement.classList.toggle('expanded');
                    }
                }
            });
        }
    }

    /**
     * Initialize drag functionality to move TOC box
     */
    function initDragFunctionality() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Skip drag functionality on mobile
        if (window.innerWidth <= 1024) return;

        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        let startPos = { x: 0, y: 0 };

        // Add drag handle to TOC heading
        const tocHeading = tocElement.querySelector('.toc-heading');
        if (tocHeading) {
            tocHeading.style.cursor = 'move';
            tocHeading.style.userSelect = 'none';
            tocHeading.title = 'Drag to move TOC';

            // Mouse events
            tocHeading.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);

            // Touch events for touch devices
            tocHeading.addEventListener('touchstart', startDragTouch, { passive: false });
            document.addEventListener('touchmove', dragTouch, { passive: false });
            document.addEventListener('touchend', stopDrag);
        }

        function startDrag(e) {
            if (e.button !== 0) return; // Only left mouse button
            e.preventDefault();

            isDragging = true;
            const rect = tocElement.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            startPos.x = e.clientX;
            startPos.y = e.clientY;

            tocElement.style.transition = 'none';
            document.body.style.userSelect = 'none';
        }

        function startDragTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];

            isDragging = true;
            const rect = tocElement.getBoundingClientRect();
            dragOffset.x = touch.clientX - rect.left;
            dragOffset.y = touch.clientY - rect.top;
            startPos.x = touch.clientX;
            startPos.y = touch.clientY;

            tocElement.style.transition = 'none';
        }

        function drag(e) {
            if (!isDragging) return;
            e.preventDefault();

            // Check if this is actually a drag (moved more than 5px)
            const moved = Math.abs(e.clientX - startPos.x) + Math.abs(e.clientY - startPos.y);
            if (moved < 5) return;

            const newX = e.clientX - dragOffset.x;
            const newY = e.clientY - dragOffset.y;

            // Keep TOC within viewport bounds
            const maxX = window.innerWidth - tocElement.offsetWidth;
            const maxY = window.innerHeight - tocElement.offsetHeight;

            const constrainedX = Math.max(0, Math.min(newX, maxX));
            const constrainedY = Math.max(0, Math.min(newY, maxY));

            tocElement.style.left = constrainedX + 'px';
            tocElement.style.top = constrainedY + 'px';
        }

        function dragTouch(e) {
            if (!isDragging) return;
            e.preventDefault();

            const touch = e.touches[0];

            // Check if this is actually a drag
            const moved = Math.abs(touch.clientX - startPos.x) + Math.abs(touch.clientY - startPos.y);
            if (moved < 5) return;

            const newX = touch.clientX - dragOffset.x;
            const newY = touch.clientY - dragOffset.y;

            // Keep TOC within viewport bounds
            const maxX = window.innerWidth - tocElement.offsetWidth;
            const maxY = window.innerHeight - tocElement.offsetHeight;

            const constrainedX = Math.max(0, Math.min(newX, maxX));
            const constrainedY = Math.max(0, Math.min(newY, maxY));

            tocElement.style.left = constrainedX + 'px';
            tocElement.style.top = constrainedY + 'px';
        }

        function stopDrag(e) {
            if (!isDragging) return;

            isDragging = false;
            tocElement.style.transition = '';
            document.body.style.userSelect = '';

            // Check if this was a click (not a drag)
            const moved = Math.abs(e.clientX - startPos.x) + Math.abs(e.clientY - startPos.y);
            if (moved < 5) {
                // This was a click, not a drag - handle toggle functionality
                if (tocElement.classList.contains('collapsed')) {
                    tocElement.classList.toggle('expanded');
                }
            }
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 1024) {
                // Reset position on mobile
                tocElement.style.left = '';
                tocElement.style.top = '';
            } else {
                // Ensure TOC stays within bounds on desktop
                const rect = tocElement.getBoundingClientRect();
                const maxX = window.innerWidth - tocElement.offsetWidth;
                const maxY = window.innerHeight - tocElement.offsetHeight;

                if (rect.left > maxX) {
                    tocElement.style.left = maxX + 'px';
                }
                if (rect.top > maxY) {
                    tocElement.style.top = maxY + 'px';
                }
            }
        });
    }

    /**
     * Future feature: Scroll spy to highlight current section
     * Uncomment and customize as needed
     */
    /*
    function initScrollSpy() {
        const tocLinks = document.querySelectorAll('.safeoid-toc a[href^="#"]');
        const headings = [];
        
        tocLinks.forEach(function(link) {
            const targetId = link.getAttribute('href').substring(1);
            const heading = document.getElementById(targetId);
            if (heading) {
                headings.push({
                    element: heading,
                    link: link,
                    top: getElementOffsetTop(heading)
                });
            }
        });
        
        if (headings.length === 0) return;
        
        function updateActiveLink() {
            const scrollTop = window.pageYOffset;
            const scrollOffset = getScrollOffset();
            
            let activeHeading = headings[0];
            
            for (let i = 0; i < headings.length; i++) {
                if (scrollTop >= headings[i].top - scrollOffset - 50) {
                    activeHeading = headings[i];
                }
            }
            
            // Remove active class from all links
            tocLinks.forEach(function(link) {
                link.classList.remove('active');
            });
            
            // Add active class to current link
            if (activeHeading) {
                activeHeading.link.classList.add('active');
            }
        }
        
        // Throttle scroll events
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    updateActiveLink();
                    ticking = false;
                });
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', onScroll);
        updateActiveLink(); // Initial call
    }
    */
    
})();
