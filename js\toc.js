/**
 * Safeoid TOC JavaScript
 * Simple vanilla JS for TOC functionality
 */

(function() {
    'use strict';

    // Wait for DOM to be ready and WordPress to load
    function waitForTOC() {
        if (document.querySelector('.floating-toc')) {
            initTOC();
        } else {
            // TOC not found, wait a bit more
            setTimeout(waitForTOC, 100);
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForTOC);
    } else {
        waitForTOC();
    }

    // Also try after window load as backup
    window.addEventListener('load', function() {
        if (document.querySelector('.floating-toc')) {
            initTOC();
        }
    });

    function initTOC() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Add body class for browsers that don't support :has()
        document.body.classList.add('has-floating-toc');

        // Initialize smooth scrolling
        initSmoothScrolling();

        // Initialize auto-collapse functionality
        initAutoCollapse();

        // Initialize toggle functionality
        initToggleFunctionality();

        // Initialize drag functionality
        initDragFunctionality();

        // Ensure TOC maintains fixed position
        ensureFixedPosition();

        // Ensure headings have IDs for TOC links to work
        ensureHeadingIds();

        // Reinitialize periodically to catch dynamic content
        setInterval(function() {
            const tocElement = document.querySelector('.floating-toc');
            if (tocElement) {
                initSmoothScrolling();
                ensureHeadingIds();
            }
        }, 2000);
    }

    /**
     * Initialize smooth scrolling for TOC links
     */
    function initSmoothScrolling() {
        // Wait a bit for DOM to be fully ready
        setTimeout(function() {
            // Direct event listeners on TOC links
            const tocLinks = document.querySelectorAll('.floating-toc a[href^="#"]');

            tocLinks.forEach(function(link) {
                // Remove any existing listeners
                link.removeEventListener('click', handleTocLinkClick);
                // Add new listener
                link.addEventListener('click', handleTocLinkClick);
            });

            // Also use event delegation as backup
            document.addEventListener('click', function(e) {
                // Check if clicked element is a TOC link
                if (e.target.matches('.floating-toc a[href^="#"]') ||
                    e.target.closest('.floating-toc a[href^="#"]')) {

                    const link = e.target.matches('.floating-toc a[href^="#"]') ?
                                e.target : e.target.closest('.floating-toc a[href^="#"]');

                    handleTocLinkClick.call(link, e);
                }
            });
        }, 100);
    }

    /**
     * Handle TOC link clicks
     */
    function handleTocLinkClick(e) {
        e.preventDefault();
        e.stopPropagation();

        const link = this;
        const targetId = link.getAttribute('href').substring(1);

        console.log('TOC Link Clicked:', targetId);

        // Try multiple ways to find the target element
        let targetElement = document.getElementById(targetId);

        if (!targetElement) {
            // Try to find by text content
            const linkText = link.textContent.trim();
            const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');

            for (let heading of headings) {
                if (heading.textContent.trim() === linkText) {
                    targetElement = heading;
                    // Add the ID for future use
                    heading.id = targetId;
                    console.log('Found heading by text and added ID:', targetId);
                    break;
                }
            }
        }

        if (!targetElement) {
            console.error('Target element not found for:', targetId);
            return;
        }

        // Set flag to prevent auto-collapse during click scrolling
        if (window.setTOCClickScrolling) {
            window.setTOCClickScrolling(true);
        }

        // Also set a more robust flag
        window.tocClickInProgress = true;
        setTimeout(() => {
            window.tocClickInProgress = false;
        }, 3000);

        const offsetTop = getElementOffsetTop(targetElement);
        const scrollOffset = getScrollOffset();
        const finalScrollTop = Math.max(0, offsetTop - scrollOffset);

        console.log('TOC Scroll Debug:', {
            targetId: targetId,
            targetElement: targetElement,
            offsetTop: offsetTop,
            scrollOffset: scrollOffset,
            finalScrollTop: finalScrollTop
        });

        // Smooth scroll to target
        window.scrollTo({
            top: finalScrollTop,
            behavior: 'smooth'
        });

        // Handle TOC state after clicking
        const tocElement = document.querySelector('.floating-toc');
        if (tocElement) {
            // Close mobile TOC after clicking
            if (window.innerWidth <= 1024) {
                document.body.classList.remove('toc-open');
            }
        }

        // Add active state to clicked link
        const allTocLinks = document.querySelectorAll('.floating-toc a[href^="#"]');
        allTocLinks.forEach(function(tocLink) {
            tocLink.classList.remove('active');
        });
        link.classList.add('active');
    }

    /**
     * Get element's offset from top of page
     */
    function getElementOffsetTop(element) {
        let offsetTop = 0;
        while (element) {
            offsetTop += element.offsetTop;
            element = element.offsetParent;
        }
        return offsetTop;
    }

    /**
     * Get scroll offset for fixed headers
     */
    function getScrollOffset() {
        let offset = 80; // Increased default offset for better positioning

        // WordPress admin bar
        const adminBar = document.getElementById('wpadminbar');
        if (adminBar && window.getComputedStyle(adminBar).position === 'fixed') {
            offset += adminBar.offsetHeight;
        }

        // Mobile TOC
        const mobileToc = document.querySelector('.floating-toc');
        if (mobileToc && window.innerWidth <= 1024) {
            offset += 60; // Mobile TOC height
        }

        // Check for common theme headers and navigation
        const stickySelectors = [
            '.sticky-header', '.fixed-header', 'header.fixed',
            '.site-header.fixed', '.main-header.fixed',
            'nav.fixed', '.navbar.fixed-top',
            '.header-sticky', '.sticky-nav'
        ];

        stickySelectors.forEach(function(selector) {
            const elements = document.querySelectorAll(selector);
            elements.forEach(function(element) {
                const style = window.getComputedStyle(element);
                if (style.position === 'fixed' && style.top === '0px') {
                    offset += element.offsetHeight;
                }
            });
        });

        // Check for any element with position fixed at top
        const allElements = document.querySelectorAll('*');
        allElements.forEach(function(element) {
            const style = window.getComputedStyle(element);
            if (style.position === 'fixed' &&
                (style.top === '0px' || parseInt(style.top) >= 0 && parseInt(style.top) < 100) &&
                element.offsetHeight > 0 && element.offsetHeight < 200) {
                // Avoid counting the TOC itself
                if (!element.classList.contains('floating-toc')) {
                    offset += element.offsetHeight;
                }
            }
        });

        return Math.min(offset, 200); // Cap at 200px to avoid excessive offset
    }

    /**
     * Initialize auto-collapse functionality
     */
    function initAutoCollapse() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        let lastScrollTop = 0;
        let scrollTimeout;
        let isUserScrolling = false;
        let isClickScrolling = false; // Flag to prevent collapse during click scrolling

        // Initially TOC is open on desktop (but not in Elementor)
        if (window.innerWidth > 1024 && !tocElement.closest('.elementor-widget-safeoid-toc')) {
            tocElement.classList.remove('collapsed');
        }

        function handleScroll() {
            // Skip auto-collapse on mobile or in Elementor widget
            if (window.innerWidth <= 1024) return;
            if (tocElement.closest('.elementor-widget-safeoid-toc')) return;

            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Don't collapse if this is a programmatic scroll from clicking TOC links
            if (isClickScrolling || window.tocClickInProgress) return;

            // Detect if user is manually scrolling
            if (Math.abs(currentScrollTop - lastScrollTop) > 10) {
                isUserScrolling = true;

                // Always collapse when scrolling, regardless of expanded state
                // Remove expanded state and add collapsed state
                tocElement.classList.remove('expanded');
                tocElement.classList.add('collapsed');

                // Clear existing timeout
                clearTimeout(scrollTimeout);

                // Set timeout to detect when scrolling stops
                scrollTimeout = setTimeout(() => {
                    isUserScrolling = false;
                }, 200);
            }

            lastScrollTop = currentScrollTop;
        }

        // Throttle scroll events
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', onScroll);

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 1024) {
                tocElement.classList.remove('collapsed', 'expanded');
            } else {
                // Reset to open state on desktop
                tocElement.classList.remove('collapsed');
            }
        });

        // Expose function to set click scrolling flag
        window.setTOCClickScrolling = function(value) {
            isClickScrolling = value;
            if (value) {
                // Clear the flag after a longer delay to ensure scroll completes
                setTimeout(() => {
                    isClickScrolling = false;
                }, 3000); // Increased from 1500ms to 3000ms
            }
        };
    }

    /**
     * Initialize toggle functionality for collapsed state
     */
    function initToggleFunctionality() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        const tocHeading = tocElement.querySelector('.toc-heading');
        const tocToggle = tocElement.querySelector('.toc-toggle');

        // Desktop: Click on heading when collapsed
        if (tocHeading) {
            tocHeading.addEventListener('click', function(e) {
                // Don't interfere with drag handle clicks
                if (e.target.classList.contains('toc-drag-handle')) {
                    return;
                }

                e.preventDefault();
                e.stopPropagation();

                if (window.innerWidth > 1024 && tocElement.classList.contains('collapsed')) {
                    tocElement.classList.toggle('expanded');
                }
            });
        }

        // Mobile: Click on toggle (keep original mobile behavior)
        if (tocToggle) {
            tocToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                if (window.innerWidth <= 1024) {
                    // Original mobile behavior - just toggle toc-open class
                    document.body.classList.toggle('toc-open');
                } else {
                    // Desktop: toggle expanded state when collapsed
                    if (tocElement.classList.contains('collapsed')) {
                        tocElement.classList.toggle('expanded');
                    }
                }
            });
        }
    }

    /**
     * Initialize drag functionality to move TOC box
     */
    function initDragFunctionality() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Skip drag functionality on mobile or in Elementor widget
        if (window.innerWidth <= 1024) return;
        if (tocElement.closest('.elementor-widget-safeoid-toc')) return;

        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        let startPos = { x: 0, y: 0 };

        // Add drag handle to TOC (if not already present)
        const tocHeading = tocElement.querySelector('.toc-heading');
        let dragHandle = tocElement.querySelector('.toc-drag-handle');

        if (tocHeading && !dragHandle) {
            // Create drag handle element
            dragHandle = document.createElement('div');
            dragHandle.className = 'toc-drag-handle';
            dragHandle.title = 'Click and drag to move';
            dragHandle.setAttribute('aria-label', 'Drag handle to move table of contents');
            tocElement.appendChild(dragHandle);
        }

        if (dragHandle) {

            // Mouse events on drag handle only
            dragHandle.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);

            // Touch events for touch devices
            dragHandle.addEventListener('touchstart', startDragTouch, { passive: false });
            document.addEventListener('touchmove', dragTouch, { passive: false });
            document.addEventListener('touchend', stopDrag);
        }

        function startDrag(e) {
            if (e.button !== 0) return; // Only left mouse button
            e.preventDefault();

            isDragging = true;
            const rect = tocElement.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            startPos.x = e.clientX;
            startPos.y = e.clientY;

            // Add visual feedback
            tocElement.classList.add('dragging');
            tocElement.style.transition = 'none';
            document.body.style.userSelect = 'none';
            document.body.style.cursor = 'move';
        }

        function startDragTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];

            isDragging = true;
            const rect = tocElement.getBoundingClientRect();
            dragOffset.x = touch.clientX - rect.left;
            dragOffset.y = touch.clientY - rect.top;
            startPos.x = touch.clientX;
            startPos.y = touch.clientY;

            tocElement.style.transition = 'none';
        }

        function drag(e) {
            if (!isDragging) return;
            e.preventDefault();

            // Check if this is actually a drag (moved more than 5px)
            const moved = Math.abs(e.clientX - startPos.x) + Math.abs(e.clientY - startPos.y);
            if (moved < 5) return;

            const newX = e.clientX - dragOffset.x;
            const newY = e.clientY - dragOffset.y;

            // Keep TOC within viewport bounds
            const maxX = window.innerWidth - tocElement.offsetWidth;
            const maxY = window.innerHeight - tocElement.offsetHeight;

            const constrainedX = Math.max(0, Math.min(newX, maxX));
            const constrainedY = Math.max(0, Math.min(newY, maxY));

            // Ensure position stays fixed
            tocElement.style.position = 'fixed';
            tocElement.style.left = constrainedX + 'px';
            tocElement.style.top = constrainedY + 'px';
        }

        function dragTouch(e) {
            if (!isDragging) return;
            e.preventDefault();

            const touch = e.touches[0];

            // Check if this is actually a drag
            const moved = Math.abs(touch.clientX - startPos.x) + Math.abs(touch.clientY - startPos.y);
            if (moved < 5) return;

            const newX = touch.clientX - dragOffset.x;
            const newY = touch.clientY - dragOffset.y;

            // Keep TOC within viewport bounds
            const maxX = window.innerWidth - tocElement.offsetWidth;
            const maxY = window.innerHeight - tocElement.offsetHeight;

            const constrainedX = Math.max(0, Math.min(newX, maxX));
            const constrainedY = Math.max(0, Math.min(newY, maxY));

            // Ensure position stays fixed
            tocElement.style.position = 'fixed';
            tocElement.style.left = constrainedX + 'px';
            tocElement.style.top = constrainedY + 'px';
        }

        function stopDrag(e) {
            if (!isDragging) return;

            isDragging = false;

            // Remove visual feedback
            tocElement.classList.remove('dragging');
            tocElement.style.transition = '';
            document.body.style.userSelect = '';
            document.body.style.cursor = '';
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 1024) {
                // Reset position on mobile
                tocElement.style.left = '';
                tocElement.style.top = '';
            } else {
                // Ensure TOC stays within bounds on desktop and maintains fixed position
                tocElement.style.position = 'fixed';
                const rect = tocElement.getBoundingClientRect();
                const maxX = window.innerWidth - tocElement.offsetWidth;
                const maxY = window.innerHeight - tocElement.offsetHeight;

                if (rect.left > maxX) {
                    tocElement.style.left = maxX + 'px';
                }
                if (rect.top > maxY) {
                    tocElement.style.top = maxY + 'px';
                }
            }
        });
    }

    /**
     * Ensure TOC maintains fixed position
     */
    function ensureFixedPosition() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Force fixed position on desktop
        if (window.innerWidth > 1024) {
            tocElement.style.position = 'fixed';

            // Set up a periodic check to ensure position stays fixed
            setInterval(function() {
                if (window.innerWidth > 1024 && tocElement.style.position !== 'fixed') {
                    tocElement.style.position = 'fixed';
                }
            }, 1000);
        }
    }

    /**
     * Ensure headings have IDs for TOC links to work
     */
    function ensureHeadingIds() {
        const tocLinks = document.querySelectorAll('.floating-toc a[href^="#"]');

        tocLinks.forEach(function(link) {
            const targetId = link.getAttribute('href').substring(1);
            let targetElement = document.getElementById(targetId);

            if (!targetElement) {
                // Try to find the heading by text content
                const linkText = link.textContent.trim();
                const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');

                headings.forEach(function(heading) {
                    if (heading.textContent.trim() === linkText && !heading.id) {
                        heading.id = targetId;
                        console.log('Added missing ID:', targetId, 'to heading:', linkText);
                    }
                });
            }
        });
    }

    /**
     * Generate heading ID from text (same logic as PHP)
     */
    function generateHeadingId(text) {
        // Convert to lowercase and replace spaces/special chars with hyphens
        let id = text.toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-')     // Replace spaces with hyphens
            .replace(/-+/g, '-')      // Remove multiple consecutive hyphens
            .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens

        // Ensure ID is not empty
        if (!id) {
            id = 'heading-' + Date.now();
        }

        return id;
    }

    /**
     * Debug function to check TOC status
     */
    window.debugTOC = function() {
        const tocElement = document.querySelector('.floating-toc');
        const tocLinks = document.querySelectorAll('.floating-toc a[href^="#"]');
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');

        console.log('=== TOC Debug Info ===');
        console.log('TOC Element:', tocElement);
        console.log('TOC Links:', tocLinks.length);
        console.log('Page Headings:', headings.length);

        const linkTargets = [];
        const missingTargets = [];

        tocLinks.forEach(function(link) {
            const href = link.getAttribute('href');
            const targetId = href.substring(1);
            const target = document.getElementById(targetId);

            linkTargets.push({
                text: link.textContent,
                href: href,
                targetId: targetId,
                targetExists: !!target,
                target: target
            });

            if (!target) {
                missingTargets.push(targetId);
            }
        });

        console.log('Link Targets:', linkTargets);
        console.log('Missing Targets:', missingTargets);

        if (missingTargets.length > 0) {
            console.warn('⚠️ Missing target elements for:', missingTargets);
            console.log('💡 Try running: ensureHeadingIds()');
        } else {
            console.log('✅ All TOC links have valid targets!');
        }

        return {
            tocElement: tocElement,
            tocLinks: tocLinks,
            headings: headings,
            linkTargets: linkTargets,
            missingTargets: missingTargets
        };
    };

    // Make ensureHeadingIds available globally for debugging
    window.ensureHeadingIds = ensureHeadingIds;

    /**
     * Future feature: Scroll spy to highlight current section
     * Uncomment and customize as needed
     */
    /*
    function initScrollSpy() {
        const tocLinks = document.querySelectorAll('.safeoid-toc a[href^="#"]');
        const headings = [];
        
        tocLinks.forEach(function(link) {
            const targetId = link.getAttribute('href').substring(1);
            const heading = document.getElementById(targetId);
            if (heading) {
                headings.push({
                    element: heading,
                    link: link,
                    top: getElementOffsetTop(heading)
                });
            }
        });
        
        if (headings.length === 0) return;
        
        function updateActiveLink() {
            const scrollTop = window.pageYOffset;
            const scrollOffset = getScrollOffset();
            
            let activeHeading = headings[0];
            
            for (let i = 0; i < headings.length; i++) {
                if (scrollTop >= headings[i].top - scrollOffset - 50) {
                    activeHeading = headings[i];
                }
            }
            
            // Remove active class from all links
            tocLinks.forEach(function(link) {
                link.classList.remove('active');
            });
            
            // Add active class to current link
            if (activeHeading) {
                activeHeading.link.classList.add('active');
            }
        }
        
        // Throttle scroll events
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    updateActiveLink();
                    ticking = false;
                });
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', onScroll);
        updateActiveLink(); // Initial call
    }
    */
    
})();
