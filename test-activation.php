<?php
/**
 * Test plugin activation without WordPress
 */

echo "Testing Safeoid TOC Plugin Activation...\n\n";

// Mock WordPress functions
function plugin_dir_path($file) { return dirname($file) . '/'; }
function plugin_dir_url($file) { return 'http://example.com/wp-content/plugins/' . basename(dirname($file)) . '/'; }
function plugin_basename($file) { return basename(dirname($file)) . '/' . basename($file); }
function register_activation_hook($file, $callback) { /* Mock */ }
function register_deactivation_hook($file, $callback) { /* Mock */ }
function add_action($hook, $callback, $priority = 10, $args = 1) { /* Mock */ }
function add_filter($hook, $callback, $priority = 10, $args = 1) { /* Mock */ }
function add_shortcode($tag, $callback) { /* Mock */ }
function __($text, $domain = 'default') { return $text; }
function get_option($option, $default = false) { return $default; }
function did_action($hook) { return false; }
function load_plugin_textdomain($domain, $deprecated, $plugin_rel_path) { /* Mock */ }

// Define ABSPATH
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/');
}

try {
    echo "1. Including plugin file...\n";
    include_once __DIR__ . '/safeoid-toc.php';
    echo "   ✅ Plugin file included successfully\n";
    
    echo "2. Checking constants...\n";
    $constants = [
        'SAFEOID_TOC_VERSION',
        'SAFEOID_TOC_PLUGIN_DIR', 
        'SAFEOID_TOC_PLUGIN_URL',
        'SAFEOID_TOC_PLUGIN_FILE',
        'SAFEOID_TOC_TEXT_DOMAIN'
    ];
    
    foreach ($constants as $constant) {
        if (defined($constant)) {
            echo "   ✅ $constant: " . constant($constant) . "\n";
        } else {
            echo "   ❌ $constant: NOT DEFINED\n";
        }
    }
    
    echo "3. Checking class...\n";
    if (class_exists('SafeoidTOC')) {
        echo "   ✅ SafeoidTOC class exists\n";
        
        echo "4. Testing instance creation...\n";
        $instance = SafeoidTOC::get_instance();
        if ($instance) {
            echo "   ✅ Plugin instance created successfully\n";
            echo "   ✅ Instance type: " . get_class($instance) . "\n";
        } else {
            echo "   ❌ Failed to create plugin instance\n";
        }
    } else {
        echo "   ❌ SafeoidTOC class not found\n";
    }
    
    echo "\n✅ ALL TESTS PASSED - Plugin should activate without errors!\n";
    
} catch (ParseError $e) {
    echo "\n❌ PARSE ERROR:\n";
    echo "   Message: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "\n❌ FATAL ERROR:\n";
    echo "   Message: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
} catch (Exception $e) {
    echo "\n❌ EXCEPTION:\n";
    echo "   Message: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . "\n";
    echo "   Line: " . $e->getLine() . "\n";
}

echo "\nTest completed.\n";
