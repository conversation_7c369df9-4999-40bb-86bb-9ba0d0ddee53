<?php
/**
 * Test file to check for PHP syntax errors
 */

// Test if the main plugin file has syntax errors
$plugin_file = __DIR__ . '/safeoid-toc.php';

if (file_exists($plugin_file)) {
    echo "Testing plugin file syntax...\n";
    
    // Check syntax using php -l
    $output = shell_exec("php -l " . escapeshellarg($plugin_file) . " 2>&1");
    
    if (strpos($output, 'No syntax errors detected') !== false) {
        echo "✅ Main plugin file syntax is OK\n";
    } else {
        echo "❌ Syntax error in main plugin file:\n";
        echo $output . "\n";
    }
} else {
    echo "❌ Plugin file not found\n";
}

// Test widget file
$widget_file = __DIR__ . '/widgets/elementor-toc-widget.php';
if (file_exists($widget_file)) {
    $output = shell_exec("php -l " . escapeshellarg($widget_file) . " 2>&1");
    
    if (strpos($output, 'No syntax errors detected') !== false) {
        echo "✅ Widget file syntax is OK\n";
    } else {
        echo "❌ Syntax error in widget file:\n";
        echo $output . "\n";
    }
}

// Test settings page
$settings_file = __DIR__ . '/inc/settings-page.php';
if (file_exists($settings_file)) {
    $output = shell_exec("php -l " . escapeshellarg($settings_file) . " 2>&1");
    
    if (strpos($output, 'No syntax errors detected') !== false) {
        echo "✅ Settings file syntax is OK\n";
    } else {
        echo "❌ Syntax error in settings file:\n";
        echo $output . "\n";
    }
}

// Test if we can include the main file without errors
echo "\nTesting file inclusion...\n";

try {
    // Simulate WordPress environment
    if (!defined('ABSPATH')) {
        define('ABSPATH', '/tmp/');
    }
    
    // Test include
    ob_start();
    include_once $plugin_file;
    $output = ob_get_clean();
    
    echo "✅ File included successfully\n";
    
} catch (ParseError $e) {
    echo "❌ Parse error: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}
