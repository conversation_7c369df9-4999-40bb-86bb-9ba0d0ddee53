<?php
/**
 * Simple test to check if plugin can be loaded
 */

// Simulate WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/');
}

// Mock WordPress functions that might be called
if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return dirname($file) . '/';
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'http://example.com/wp-content/plugins/' . basename(dirname($file)) . '/';
    }
}

if (!function_exists('plugin_basename')) {
    function plugin_basename($file) {
        return basename(dirname($file)) . '/' . basename($file);
    }
}

if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) {
        // Mock function
    }
}

if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) {
        // Mock function
    }
}

if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $args = 1) {
        // Mock function
    }
}

if (!function_exists('add_filter')) {
    function add_filter($hook, $callback, $priority = 10, $args = 1) {
        // Mock function
    }
}

if (!function_exists('add_shortcode')) {
    function add_shortcode($tag, $callback) {
        // Mock function
    }
}

if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        return $default;
    }
}

if (!function_exists('did_action')) {
    function did_action($hook) {
        return false;
    }
}

if (!function_exists('version_compare')) {
    function version_compare($version1, $version2, $operator = null) {
        return \version_compare($version1, $version2, $operator);
    }
}

echo "Testing plugin loading...\n";

try {
    // Test if we can include the plugin file
    ob_start();
    include_once __DIR__ . '/safeoid-toc.php';
    $output = ob_get_clean();
    
    echo "✅ Plugin loaded successfully!\n";
    
    // Check if class exists
    if (class_exists('SafeoidTOC')) {
        echo "✅ SafeoidTOC class exists\n";
        
        // Check if instance can be created
        $instance = SafeoidTOC::get_instance();
        if ($instance) {
            echo "✅ Plugin instance created successfully\n";
        } else {
            echo "❌ Failed to create plugin instance\n";
        }
    } else {
        echo "❌ SafeoidTOC class not found\n";
    }
    
    // Check constants
    $constants = [
        'SAFEOID_TOC_VERSION',
        'SAFEOID_TOC_PLUGIN_DIR', 
        'SAFEOID_TOC_PLUGIN_URL',
        'SAFEOID_TOC_PLUGIN_FILE',
        'SAFEOID_TOC_TEXT_DOMAIN'
    ];
    
    foreach ($constants as $constant) {
        if (defined($constant)) {
            echo "✅ Constant $constant defined: " . constant($constant) . "\n";
        } else {
            echo "❌ Constant $constant not defined\n";
        }
    }
    
} catch (ParseError $e) {
    echo "❌ Parse error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "❌ Fatal error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine() . "\n";
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine() . "\n";
}

echo "\nTest completed.\n";
