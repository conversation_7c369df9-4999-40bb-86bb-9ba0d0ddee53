/**
 * Safeoid TOC Styles
 * Responsive Table of Contents styling
 */

/* Push main content on desktop when TOC is present */
body:has(.floating-toc) {
    padding-left: 0;
}

/* Fallback for browsers that don't support :has() */
body.has-floating-toc {
    padding-left: 0;
}

.floating-toc {
    position: fixed !important;
    top: 60px;
    left: 20px;
    width: 250px;
    background: var(--safeoid-toc-bg-color, #fff);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    z-index: 9999;
    font-family: Arial, sans-serif;
    color: var(--safeoid-toc-text-color, #111);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    /* Initially open state */
    opacity: 1;
    visibility: visible;
}

.floating-toc .toc-heading {
    font-size: 16px;
    font-weight: bold;
    margin: 0 0 10px 0;
    padding: 0;
    color: var(--safeoid-toc-text-color, #111);
    position: relative;
}

/* Drag functionality styles - only for floating TOC, not Elementor widget */
@media (min-width: 1025px) {
    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc) {
        position: relative;
    }

    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc) .toc-heading {
        cursor: pointer;
        user-select: none;
        position: relative;
        padding-right: 25px; /* Make space for drag handle */
    }

    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc).collapsed .toc-heading:hover {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        padding: 2px 4px 2px 4px;
        margin: -2px -4px 8px -4px;
    }

    /* Drag handle - more obvious design - only for floating TOC */
    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc) .toc-drag-handle {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        cursor: move;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        transition: all 0.2s ease;
        user-select: none;
        opacity: 0.7;
    }

    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc) .toc-drag-handle:hover {
        background-color: rgba(0, 0, 0, 0.1);
        border-color: rgba(0, 0, 0, 0.2);
        opacity: 1;
        transform: scale(1.1);
    }

    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc) .toc-drag-handle::before {
        content: "⋮⋮";
        font-size: 12px;
        color: #666;
        line-height: 1;
        font-weight: bold;
    }

    /* Add a subtle animation to hint it's draggable - only for floating TOC */
    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc) .toc-drag-handle {
        animation: dragHint 3s ease-in-out infinite;
    }

    @keyframes dragHint {
        0%, 90%, 100% { transform: scale(1); }
        95% { transform: scale(1.05); }
    }

    /* Stop animation on hover */
    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc) .toc-drag-handle:hover {
        animation: none;
    }

    /* Tooltip for drag handle - only for floating TOC */
    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc) .toc-drag-handle::after {
        content: attr(title);
        position: absolute;
        bottom: -30px;
        right: 0;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease;
        z-index: 10001;
    }

    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc) .toc-drag-handle:hover::after {
        opacity: 1;
    }

    /* Better visual feedback when dragging - only for floating TOC */
    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc).dragging {
        position: fixed !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        transform: scale(1.02);
        z-index: 10000;
    }

    .floating-toc:not(.elementor-widget-safeoid-toc .floating-toc).dragging .toc-drag-handle {
        background-color: rgba(0, 123, 255, 0.2);
        border-color: rgba(0, 123, 255, 0.4);
    }
}

.floating-toc .toc-toggle {
    display: none;
}

/* Collapsed state styles */
.floating-toc.collapsed {
    position: fixed !important;
    width: auto;
    min-width: 200px;
    max-width: 300px;
    transition: width 0.3s ease;
}

.floating-toc.collapsed .toc-content {
    display: none;
}

.floating-toc.collapsed .toc-heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    margin-bottom: 0;
    padding: 5px 25px 5px 0; /* Right padding for drag handle */
}

.floating-toc.collapsed .toc-heading::after {
    content: "▼";
    font-size: 12px;
    transition: transform 0.3s ease;
    margin-left: 10px;
}

.floating-toc.collapsed.expanded .toc-heading::after {
    transform: rotate(180deg);
}

.floating-toc.collapsed.expanded .toc-content {
    display: block;
    animation: slideDown 0.3s ease;
}

.floating-toc.expanded {
    position: fixed !important;
    width: 250px; /* Return to original width when expanded */
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        overflow: hidden;
    }
    to {
        opacity: 1;
        max-height: 500px;
        overflow: visible;
    }
}

.floating-toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.floating-toc ul li {
    margin: 8px 0;
}

.floating-toc ul li a {
    text-decoration: none;
    color: var(--safeoid-toc-link-color, #0073e6);
    font-size: 14px;
}

.floating-toc ul li a:hover {
    text-decoration: underline;
    color: var(--safeoid-toc-link-hover-color, #005bb5);
}

.floating-toc ul li a.active {
    font-weight: bold;
    color: var(--safeoid-toc-link-active-color, #d63638);
    background-color: rgba(214, 54, 56, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
}

@media (max-width: 1024px) {
    /* Remove desktop body padding on mobile */
    body:has(.floating-toc),
    body.has-floating-toc {
        padding-left: 0;
        margin-top: 60px; /* pushes content below TOC */
    }

    .floating-toc {
        top: 0;
        left: 0;
        width: 100%;
        border-radius: 0;
        border: none;
        padding: 12px 16px;
        /* Disable auto-collapse on mobile - keep original behavior */
    }

    .floating-toc .toc-heading {
        display: none;
    }

    .floating-toc .toc-toggle {
        display: block;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
        color: var(--safeoid-toc-text-color, #111);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .floating-toc .toc-toggle::after {
        content: "▼";
        font-size: 12px;
        transition: transform 0.3s ease;
    }

    body.toc-open .floating-toc .toc-toggle::after {
        transform: rotate(180deg);
    }

    .floating-toc .toc-content {
        display: none;
        margin-top: 10px;
    }

    body.toc-open .floating-toc .toc-content {
        display: block;
    }

    /* Disable collapsed state on mobile - keep original mobile behavior */
    .floating-toc.collapsed,
    .floating-toc.expanded {
        width: 100%;
        min-width: auto;
        max-width: none;
        top: 0;
        left: 0;
    }

    .floating-toc.collapsed .toc-heading,
    .floating-toc.expanded .toc-heading {
        display: none;
    }

    .floating-toc.collapsed .toc-toggle,
    .floating-toc.expanded .toc-toggle {
        display: flex;
    }

    .floating-toc.collapsed .toc-content {
        display: none;
    }

    .floating-toc.expanded .toc-content {
        display: none;
    }

    /* Only show content when body has toc-open class */
    body.toc-open .floating-toc.collapsed .toc-content,
    body.toc-open .floating-toc.expanded .toc-content {
        display: block;
    }
}

/* Customizable colors via settings */
.floating-toc {
    color: var(--safeoid-toc-text-color, #111);
    background-color: var(--safeoid-toc-bg-color, #ffffff);
}

.floating-toc a {
    color: var(--safeoid-toc-link-color, #0073e6);
}

.floating-toc a:hover {
    color: var(--safeoid-toc-link-hover-color, #005bb5);
}

/* Elementor widget styles */
.elementor-widget-safeoid-toc .floating-toc {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    width: 100% !important;
    height: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    /* Disable drag functionality in Elementor */
    pointer-events: auto;
}

.elementor-widget-safeoid-toc .floating-toc .toc-toggle {
    display: none !important;
}

.elementor-widget-safeoid-toc .floating-toc .toc-drag-handle {
    display: none !important;
}

/* Ensure Elementor styles take precedence */
.elementor-widget-safeoid-toc .floating-toc .toc-heading {
    margin: 0 0 15px 0 !important;
    padding: 0 !important;
}

.elementor-widget-safeoid-toc .floating-toc .toc-content {
    display: block !important;
}

/* Disable auto-collapse in Elementor */
.elementor-widget-safeoid-toc .floating-toc.collapsed {
    width: 100% !important;
}

.elementor-widget-safeoid-toc .floating-toc.collapsed .toc-content {
    display: block !important;
}

/* Print styles */
@media print {
    .floating-toc {
        display: none !important;
    }
}
