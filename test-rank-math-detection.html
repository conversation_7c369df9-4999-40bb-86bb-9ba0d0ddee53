<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rank Math TOC Detection Test</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .debug-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <!-- Enhanced TOC with Rank Math Compatible Classes -->
    <div class="floating-toc ez-toc-container toc-container" itemscope itemtype="https://schema.org/ItemList">
        <h3 class="toc-heading ez-toc-title" itemprop="name">Table of Contents</h3>
        <div class="toc-drag-handle" title="Click and drag to move" aria-label="Drag handle to move table of contents"></div>
        <div class="toc-toggle">☰ Table of Contents</div>
        <div class="toc-content">
            <ul class="ez-toc-list toc-list">
                <li class="ez-toc-page-1 ez-toc-heading-level-2" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                    <a class="ez-toc-link" href="#rank-math-detection" itemprop="url"><span itemprop="name">Rank Math Detection</span></a>
                    <meta itemprop="position" content="1">
                </li>
                <li class="ez-toc-page-1 ez-toc-heading-level-2" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                    <a class="ez-toc-link" href="#heading-extraction" itemprop="url"><span itemprop="name">Heading Extraction</span></a>
                    <meta itemprop="position" content="2">
                </li>
                <li class="ez-toc-page-1 ez-toc-heading-level-2" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                    <a class="ez-toc-link" href="#schema-markup" itemprop="url"><span itemprop="name">Schema Markup</span></a>
                    <meta itemprop="position" content="3">
                </li>
                <li class="ez-toc-page-1 ez-toc-heading-level-2" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                    <a class="ez-toc-link" href="#testing-results" itemprop="url"><span itemprop="name">Testing Results</span></a>
                    <meta itemprop="position" content="4">
                </li>
            </ul>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content">
        <h1>Rank Math TOC Detection Test</h1>
        
        <div class="debug-info">
            <h4>🔍 Debug Information</h4>
            <p><strong>Purpose:</strong> This page tests Rank Math SEO plugin detection of our TOC plugin.</p>
            <p><strong>What to check:</strong></p>
            <ul>
                <li>Rank Math should detect TOC presence (green checkmark)</li>
                <li>Schema markup should validate in Google's Rich Results Test</li>
                <li>TOC links should work properly</li>
                <li>CSS classes should match Easy Table of Contents format</li>
            </ul>
        </div>

        <h2 id="rank-math-detection">Rank Math Detection</h2>
        <p>For Rank Math to detect our TOC plugin, we implemented the following:</p>
        
        <h3>1. Filter Hook Implementation</h3>
        <p>Added the critical filter: <code>add_filter('rank_math/researches/toc_plugins', array($this, 'register_with_rank_math'));</code></p>
        
        <h3>2. CSS Classes Compatibility</h3>
        <p>Used the same CSS classes as Easy Table of Contents:</p>
        <ul>
            <li><code>ez-toc-container</code> - Main container</li>
            <li><code>ez-toc-title</code> - TOC heading</li>
            <li><code>ez-toc-list</code> - TOC list</li>
            <li><code>ez-toc-link</code> - TOC links</li>
            <li><code>ez-toc-heading-level-X</code> - Heading level classes</li>
        </ul>

        <h2 id="heading-extraction">Heading Extraction</h2>
        <p>Fixed the "No headings found" issue with multiple approaches:</p>
        
        <h3>1. Improved Regex Pattern</h3>
        <p>Enhanced the heading detection regex to be more reliable.</p>
        
        <h3>2. Alternative Extraction Method</h3>
        <p>Added fallback method using simpler regex patterns if the main method fails.</p>
        
        <h3>3. Debug Logging</h3>
        <p>Added comprehensive logging to help troubleshoot heading detection issues.</p>

        <h2 id="schema-markup">Schema Markup</h2>
        <p>Implemented proper Schema.org structured data:</p>
        
        <h3>1. Microdata Schema</h3>
        <ul>
            <li><code>itemscope itemtype="https://schema.org/ItemList"</code> on container</li>
            <li><code>itemprop="itemListElement"</code> on each list item</li>
            <li><code>itemprop="position"</code> for proper ordering</li>
            <li><code>itemprop="name"</code> and <code>itemprop="url"</code> for links</li>
        </ul>
        
        <h3>2. JSON-LD Integration</h3>
        <p>Added JSON-LD schema integration with Rank Math's existing structured data.</p>

        <h2 id="testing-results">Testing Results</h2>
        <p>To verify the fixes work:</p>
        
        <div class="debug-info">
            <h4>✅ WordPress Testing Steps</h4>
            <ol>
                <li><strong>Install plugin</strong> in WordPress with Rank Math active</li>
                <li><strong>Add TOC</strong> to a post/page (auto-insert or shortcode)</li>
                <li><strong>Check Rank Math analysis</strong> - should show green checkmark for TOC</li>
                <li><strong>Use debug shortcode:</strong> <code>[safeoid_toc_debug]</code></li>
                <li><strong>Check browser console</strong> for any JavaScript errors</li>
                <li><strong>Validate schema</strong> with Google's Rich Results Test</li>
            </ol>
        </div>
        
        <div class="debug-info">
            <h4>🔧 If Still Not Working</h4>
            <p>If Rank Math still doesn't detect the TOC:</p>
            <ul>
                <li>Check if the filter hook is being called</li>
                <li>Verify headings are being found in content</li>
                <li>Ensure CSS classes are present in HTML output</li>
                <li>Check for plugin conflicts</li>
                <li>Use the debug shortcode for detailed information</li>
            </ul>
        </div>

        <h3>Expected Rank Math Behavior</h3>
        <p>After implementing these fixes, Rank Math should:</p>
        <ul class="success">
            <li>✅ Show green checkmark for "Table of Contents"</li>
            <li>✅ Include TOC in SEO analysis</li>
            <li>✅ Recognize structured data</li>
            <li>✅ Stop showing "You don't seem to be using a Table of Contents plugin"</li>
        </ul>
    </div>

    <script src="js/toc.js"></script>
    <script>
        // Test schema markup validation
        function validateSchema() {
            const tocContainer = document.querySelector('[itemscope][itemtype="https://schema.org/ItemList"]');
            const listItems = document.querySelectorAll('[itemprop="itemListElement"]');
            
            console.log('Schema Validation:');
            console.log('TOC Container with ItemList schema:', !!tocContainer);
            console.log('List items with itemListElement:', listItems.length);
            
            listItems.forEach((item, index) => {
                const name = item.querySelector('[itemprop="name"]');
                const url = item.querySelector('[itemprop="url"]');
                const position = item.querySelector('[itemprop="position"]');
                
                console.log(`Item ${index + 1}:`, {
                    name: name ? name.textContent : 'Missing',
                    url: url ? url.href : 'Missing',
                    position: position ? position.content : 'Missing'
                });
            });
        }
        
        // Run validation on page load
        document.addEventListener('DOMContentLoaded', validateSchema);
        
        // Test CSS classes
        function validateCSSClasses() {
            const requiredClasses = [
                'ez-toc-container',
                'ez-toc-title', 
                'ez-toc-list',
                'ez-toc-link',
                'ez-toc-heading-level-2'
            ];
            
            console.log('CSS Classes Validation:');
            requiredClasses.forEach(className => {
                const elements = document.querySelectorAll('.' + className);
                console.log(`${className}:`, elements.length > 0 ? '✅ Found' : '❌ Missing');
            });
        }
        
        // Run CSS validation
        document.addEventListener('DOMContentLoaded', validateCSSClasses);
    </script>
</body>
</html>
